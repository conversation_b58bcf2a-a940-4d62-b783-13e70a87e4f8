package aohlog

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// default to info level logger
var defaultLogger = zap.Must(newZapLogger(zap.InfoLevel))

func Debug(msg string, fields ...zap.Field) { defaultLogger.Debug(msg, fields...) }
func Info(msg string, fields ...zap.Field)  { defaultLogger.Info(msg, fields...) }
func Warn(msg string, fields ...zap.Field)  { defaultLogger.Warn(msg, fields...) }
func Error(msg string, fields ...zap.Field) { defaultLogger.Error(msg, fields...) }
func Panic(msg string, fields ...zap.Field) { defaultLogger.Panic(msg, fields...) }
func Fatal(msg string, fields ...zap.Field) { defaultLogger.Fatal(msg, fields...) }

func Debugf(template string, args ...interface{}) { defaultLogger.Sugar().Debugf(template, args...) }
func Infof(template string, args ...interface{})  { defaultLogger.Sugar().Infof(template, args...) }
func Warnf(template string, args ...interface{})  { defaultLogger.Sugar().Warnf(template, args...) }
func Errorf(template string, args ...interface{}) { defaultLogger.Sugar().Errorf(template, args...) }
func Panicf(template string, args ...interface{}) { defaultLogger.Sugar().Panicf(template, args...) }
func Fatalf(template string, args ...interface{}) { defaultLogger.Sugar().Fatalf(template, args...) }

// SetDevelopment set logger to development mode
func SetDevelopment() {
	defaultLogger = zap.Must(newZapLogger(zap.DebugLevel))
}

// SetProduction set logger to production mode
func SetProduction() {
	defaultLogger = zap.Must(newZapLogger(zap.InfoLevel))
}

func Get() *zap.Logger {
	return defaultLogger
}

// WithOptions override the default logger with options parameter
func WithOptions(options ...zap.Option) {
	defaultLogger = defaultLogger.WithOptions(options...)
}

// newZapLogger init preconfigured zap logger.
// For development, set l to zapcore.DebugLevel.
func newZapLogger(l zapcore.Level) (*zap.Logger, error) {
	// Default to production
	isDev := false
	encoding := "json"
	encodeLevel := zapcore.CapitalLevelEncoder

	// Set development to TRUE if its DebugLevel
	if l == zapcore.DebugLevel {
		isDev = true
		encoding = "console"
		encodeLevel = zapcore.CapitalColorLevelEncoder
	}
	encodeConfig := zapcore.EncoderConfig{
		TimeKey:        "ts",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    encodeLevel,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.MillisDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}
	config := zap.Config{
		Level:            zap.NewAtomicLevelAt(l),
		Development:      isDev,
		Sampling:         nil,
		Encoding:         encoding,
		EncoderConfig:    encodeConfig,
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
	}
	logger, err := config.Build(zap.AddCallerSkip(1), zap.AddStacktrace(zap.DPanicLevel))
	return logger, err
}
