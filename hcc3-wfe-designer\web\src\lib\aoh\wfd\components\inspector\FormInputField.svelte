<script lang="ts">
	import { ui, dia } from "rappid/rappid";
	import * as Command from "$lib/aoh/wfd/components/ui/command/index.js";
	import * as Popover from "$lib/aoh/wfd/components/ui/popover/index.js";
	import * as Dialog from "$lib/aoh/wfd/components/ui/dialog/index.js";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button";
	import { formStore } from "$lib/aoh/wfd/stores/forms";
	import { oneDark } from "$lib/aoh/wfd/codemirror/theme";
	import CodeMirror from "svelte-codemirror-editor";
	import { json } from "@codemirror/lang-json";
	import FormPreview from "$lib/aoh/wfd/components/inspector/FormPreview.svelte";

	let { inspector }: { inspector: ui.Inspector } = $props();
	const cell = inspector.options.cell as dia.Cell;

	let open = $state(false);
	let value = $state(cell.attr("data/formTemplate"));
	let convertedFormJson = $state(JSON.stringify(cell.attr("data/typeOptions/FormJSON"), null, 2));

	const selectedValue = $derived($formStore.find((f) => f.id === value)?.name);
	const formJson = $derived($formStore.find((f) => f.id === value)?.form_json);

	const applyChangeToCell = () => {
		cell.attr("data/formTemplate", value);
		convertedFormJson = JSON.stringify(formJson, null, 2);
		cell.removeAttr("data/bindFields");
		cell.removeAttr("data/expressionFields");
		cell.removeAttr("data/typeOptions");
		cell.attr("data/typeOptions/FormJSON", formJson);
	};

	const closeAndUpdateValue = () => {
		open = false;
		// shad-cn popover closing animation is slower than jointJS inspector refresh
		// so we need to delay the applyChangeToCell function
		setTimeout(() => {
			applyChangeToCell();
		}, 200);
	};
</script>

<Label for="type">Form Template</Label>
<Popover.Root bind:open>
	<Popover.Trigger role="combobox" class={buttonVariants({ variant: "outline" }) + " w-full justify-between"}>
		{selectedValue || "Select a form..."}
		<span class="icon-[fa6-solid--caret-down]" aria-hidden="true"></span>
	</Popover.Trigger>
	<Popover.Content class="p-0 w-[310px]">
		<Command.Root>
			<Command.Input placeholder="Search forms..." />
			<Command.List>
				<Command.Empty>No forms found.</Command.Empty>
				<Command.Group>
					{#each $formStore as form}
						<Command.Item
							value={form.id}
							class="aria-selected:bg-primary aria-selected:text-primary-foreground"
							onSelect={() => {
								value = form.id;
								closeAndUpdateValue();
							}}
						>
							{form.name}
						</Command.Item>
					{/each}
				</Command.Group>
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
<div class="flex items-center justify-between pt-2">
	<Label>Form JSON</Label>
	<Dialog.Root>
		<Dialog.Trigger class={buttonVariants({ variant: "secondary" })}>Preview</Dialog.Trigger>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>Form Preview</Dialog.Title>
			</Dialog.Header>
			<div class="max-h-[800px] overflow-auto">
				<FormPreview {formJson}></FormPreview>
			</div>
		</Dialog.Content>
	</Dialog.Root>
</div>
<CodeMirror
	class="mt-2 normal-case"
	theme={oneDark}
	bind:value={convertedFormJson}
	lang={json()}
	readonly
	styles={{
		"&": {
			fontSize: "12px",
		},
	}}
/>
