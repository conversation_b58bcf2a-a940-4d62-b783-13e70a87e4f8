package handler

import (
	"bytes"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/mssfoobar/app/wfe/internal/model"
	mm "github.com/mssfoobar/app/wfe/internal/model/mock"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

var (
	testId = uuid.New()

	testWorkflowTemplateReq = workflowTemplateReqBody{
		Name:         "testWorkflowTemplate",
		WorkflowJson: []byte(`{"name":"workflowJSON"}`),
		DesignerJson: []byte(`{"name":"designer<PERSON><PERSON><PERSON>"}`),
		OccLock:      0,
	}

	testWorkflowTemplateResp = model.WorkflowTemplateResponse{
		Id:           testId,
		Name:         testWorkflowTemplateReq.Name,
		WorkflowJson: testWorkflowTemplateReq.Workflow<PERSON>son,
		DesignerJson: testWorkflowTemplateReq.DesignerJson,
	}
)

func TestRegisterWorkflowTemplate(t *testing.T) {
	factory := model.Factory{}
	handler := RegisterWorkflowTemplate(&factory, "http://localhost:8080")

	req, err := http.NewRequest(http.MethodPut, "/save", nil)
	if err != nil {
		t.Fatal(err)
	}

	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)

	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestWorkflowTemplate_Save(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockWorkflowTemplateStore(ctrl)
	wf := WorkflowTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/save",
			bytes.NewReader(util.StructToBytes(testWorkflowTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().SaveWorkflowTemplate(gomock.Any(), gomock.Any()).Return(&testWorkflowTemplateResp, nil)
		wf.Save(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToMap(testWorkflowTemplateResp), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid request body, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/save",
			bytes.NewReader(util.StructToBytes(workflowTemplateReqBody{})),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		wf.Save(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/save",
			bytes.NewReader(util.StructToBytes(testWorkflowTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		wf.Save(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/save",
			bytes.NewReader(util.StructToBytes(testWorkflowTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().SaveWorkflowTemplate(gomock.Any(), gomock.Any()).Return(nil, errors.New("test error"))
		wf.Save(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWorkflowTemplate_Publish(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockWorkflowTemplateStore(ctrl)
	wf := WorkflowTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/publish",
			bytes.NewReader(util.StructToBytes(testWorkflowTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().PublishWorkflowTemplate(gomock.Any(), gomock.Any()).Return(&testWorkflowTemplateResp, nil)
		wf.Publish(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToMap(testWorkflowTemplateResp), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid request body, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/publish",
			bytes.NewReader(util.StructToBytes(workflowTemplateReqBody{})),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		wf.Publish(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/publish",
			bytes.NewReader(util.StructToBytes(testWorkflowTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		wf.Publish(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/publish",
			bytes.NewReader(util.StructToBytes(testWorkflowTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().PublishWorkflowTemplate(gomock.Any(), gomock.Any()).Return(nil, errors.New("test error"))
		wf.Publish(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWorkflowTemplate_List(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockWorkflowTemplateStore(ctrl)
	wf := WorkflowTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			ListWorkflowTemplate(gomock.Any(), gomock.Any()).
			Return(&model.ListWorkflowTemplateResponse{
				WorkflowTemplates: []model.WorkflowTemplateResponse{testWorkflowTemplateResp},
				TotalCount:        0,
			}, nil)
		wf.List(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToSlice([]any{testWorkflowTemplateResp}), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/?page=a&size=b", nil)
		r.Header.Add("Content-Type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		wf.List(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		wf.List(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			ListWorkflowTemplate(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("test error"))
		wf.List(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWorkflowTemplate_Get(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockWorkflowTemplateStore(ctrl)
	wf := WorkflowTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			GetWorkflowTemplate(gomock.Any(), gomock.Any()).
			Return(&testWorkflowTemplateResp, nil)
		wf.Get(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToMap(testWorkflowTemplateResp), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{template_id}", nil),
			map[string]string{"template_id": "123"})
		r.Header.Add("Content-Type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		wf.Get(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		wf.Get(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			GetWorkflowTemplate(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("test error"))
		wf.Get(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWorkflowTemplate_Delete(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockWorkflowTemplateStore(ctrl)
	wf := WorkflowTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodDelete, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().DeleteWorkflowTemplate(gomock.Any(), gomock.Any()).Return(nil)
		wf.Delete(w, r)
		require.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodDelete, "/{template_id}", nil),
			map[string]string{"template_id": "123"})
		r.Header.Add("Content-Type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		wf.Delete(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodDelete, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		wf.Delete(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodDelete, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().DeleteWorkflowTemplate(gomock.Any(), gomock.Any()).Return(errors.New("test error"))
		wf.Delete(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}
