<script lang="ts">
	import { dia } from "rappid/rappid";
	import * as AlertDialog from "$lib/aoh/wfd/components/ui/alert-dialog/index.js";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button/index.js";
	import { workflowData } from "$lib/aoh/wfd/stores/workflowData";

	let { graph }: { graph: dia.Graph } = $props();
	let open = $state(false);

	const newBPMN = () => {
		open = false;
		graph.clear();
		$workflowData.name = "";
	};
</script>

<AlertDialog.Root bind:open>
	<AlertDialog.Trigger class={buttonVariants({ variant: "secondary" })}>New BPMN</AlertDialog.Trigger>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Create New BPMN?</AlertDialog.Title>
			<AlertDialog.Description>This will discard changes you made to the current diagram</AlertDialog.Description>
		</AlertDialog.Header>
		<AlertDialog.Footer>
			<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
			<AlertDialog.Action onclick={newBPMN}>OK</AlertDialog.Action>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>
