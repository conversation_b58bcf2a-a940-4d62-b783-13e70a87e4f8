package temporal

import (
	"context"
	"errors"
	"fmt"
	"math"
	"testing"

	"github.com/mssfoobar/app/wfe/internal/config"
	constant "github.com/mssfoobar/app/wfe/internal/wfm/common"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.temporal.io/api/common/v1"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/api/failure/v1"
	"go.temporal.io/api/history/v1"
	"go.temporal.io/api/workflow/v1"
	"go.temporal.io/api/workflowservice/v1"
	"go.temporal.io/sdk/mocks"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestNewService(t *testing.T) {
	svc := NewService(&mocks.Client{}, config.Temporal{})
	require.Equal(t, &ServiceImpl{client: &mocks.Client{}, conf: config.Temporal{}}, svc)
}

func TestServiceImpl_ExecuteWorkflow(t *testing.T) {
	mockClient := mocks.Client{}
	t.Run("expect no error", func(t *testing.T) {
		mockRun := mocks.WorkflowRun{}
		mockRun.On("GetID").Return("testWorkflowId")
		mockClient.On("ExecuteWorkflow", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&mockRun, nil).Once()
		svc := ServiceImpl{client: &mockClient, conf: config.Temporal{TaskQueue: "testTaskQueue"}}
		run, err := svc.ExecuteWorkflow(context.Background(), []byte(`{"start":"testStart"}`), nil)
		require.NoError(t, err)
		require.Equal(t, "testWorkflowId", run.WorkflowId)
	})
	t.Run("expect temporal client error", func(t *testing.T) {
		mockClient.On("ExecuteWorkflow", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("test error")).Once()
		svc := ServiceImpl{client: &mockClient, conf: config.Temporal{TaskQueue: "testTaskQueue"}}
		_, err := svc.ExecuteWorkflow(context.Background(), []byte(`{"start":"testStart"}`), nil)
		require.Error(t, err)
		require.Equal(t, "test error", err.Error())
	})
	t.Run("expect json unmarshal error", func(t *testing.T) {
		svc := ServiceImpl{client: &mockClient, conf: config.Temporal{TaskQueue: "testTaskQueue"}}
		_, err := svc.ExecuteWorkflow(context.Background(), []byte(fmt.Sprintf(`{"variables": %f}`, math.Inf(1))), nil)
		require.Error(t, err)
		require.Equal(t, "invalid character '+' looking for beginning of value", err.Error())
	})
}

func TestServiceImpl_SignalWorkflow(t *testing.T) {
	mockClient := mocks.Client{}
	t.Run("expect no error", func(t *testing.T) {
		mockClient.On("SignalWorkflow", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		svc := ServiceImpl{client: &mockClient, conf: config.Temporal{TaskQueue: "testTaskQueue"}}
		err := svc.SignalWorkflow(context.Background(), "testWorkflowId", "testSignal", map[string]string{"testKey": "testValue"})
		require.NoError(t, err)
	})
	t.Run("expect temporal client error", func(t *testing.T) {
		mockClient.On("SignalWorkflow", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(errors.New("test error")).Once()
		svc := ServiceImpl{client: &mockClient, conf: config.Temporal{TaskQueue: "testTaskQueue"}}
		err := svc.SignalWorkflow(context.Background(), "testWorkflowId", "testSignal", map[string]string{"testKey": "testValue"})
		require.Error(t, err)
		require.Equal(t, "test error", err.Error())
	})
}

func TestServiceImpl_TerminateWorkflow(t *testing.T) {
	mockClient := mocks.Client{}
	t.Run("expect no error", func(t *testing.T) {
		mockClient.On("TerminateWorkflow", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		svc := ServiceImpl{client: &mockClient, conf: config.Temporal{TaskQueue: "testTaskQueue"}}
		err := svc.TerminateWorkflow(context.Background(), "testWorkflowId", "testReason")
		require.NoError(t, err)
	})
	t.Run("expect temporal client error", func(t *testing.T) {
		mockClient.On("TerminateWorkflow", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(errors.New("test error")).Once()
		svc := ServiceImpl{client: &mockClient, conf: config.Temporal{TaskQueue: "testTaskQueue"}}
		err := svc.TerminateWorkflow(context.Background(), "testWorkflowId", "testReason")
		require.Error(t, err)
		require.Equal(t, "test error", err.Error())
	})
}

func TestGetWorkflowEventAttributes(t *testing.T) {
}

func TestServiceImpl_ListOpenWorkflow(t *testing.T) {
	mockClient := mocks.Client{}
	mockClient.On("ListWorkflow", mock.Anything, mock.Anything).Return(&workflowservice.ListWorkflowExecutionsResponse{
		Executions: []*workflow.WorkflowExecutionInfo{
			{
				Execution: &common.WorkflowExecution{
					WorkflowId: "testWorkflowId",
				},
				StartTime: &timestamppb.Timestamp{},
			},
		},
	}, nil)

	service := NewService(&mockClient, config.Temporal{})
	workflows, count, err := service.ListOpenWorkflow(context.Background(), 1, 2, true)
	require.NoError(t, err)
	require.Equal(t, 1, count)
	require.Equal(t, "testWorkflowId", workflows[0].WorkflowId)
}

func Test_getHistoryEvent(t *testing.T) {
	t.Run("Workflow Execution Started", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_WORKFLOW_EXECUTION_STARTED,
			Attributes: &history.HistoryEvent_WorkflowExecutionStartedEventAttributes{
				WorkflowExecutionStartedEventAttributes: &history.WorkflowExecutionStartedEventAttributes{
					Input: &common.Payloads{
						Payloads: []*common.Payload{
							{
								Data: []byte(`{"key":"value"}`),
							},
						},
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_WORKFLOW_EXECUTION_STARTED.String(), hist.EventType)
		require.Equal(t, map[string]any{constant.WorkflowInput: []any{map[string]any{"key": "value"}}}, *hist.Attributes)
	})

	t.Run("Workflow Execution Completed", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED,
			Attributes: &history.HistoryEvent_WorkflowExecutionCompletedEventAttributes{
				WorkflowExecutionCompletedEventAttributes: &history.WorkflowExecutionCompletedEventAttributes{
					Result: &common.Payloads{
						Payloads: []*common.Payload{
							{
								Data: []byte(`{"key":"value"}`),
							},
						},
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED.String(), hist.EventType)
		require.Equal(t, map[string]any{constant.WorkflowResult: []any{map[string]any{"key": "value"}}}, *hist.Attributes)
	})

	t.Run("Workflow Execution Failed", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_WORKFLOW_EXECUTION_FAILED,
			Attributes: &history.HistoryEvent_WorkflowExecutionFailedEventAttributes{
				WorkflowExecutionFailedEventAttributes: &history.WorkflowExecutionFailedEventAttributes{
					Failure: &failure.Failure{
						Message: "test fail message",
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_WORKFLOW_EXECUTION_FAILED.String(), hist.EventType)
		require.Equal(t, map[string]any{constant.WorkflowFailure: "test fail message"}, *hist.Attributes)
	})

	t.Run("Workflow Execution Canceled", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_WORKFLOW_EXECUTION_CANCELED,
			Attributes: &history.HistoryEvent_WorkflowExecutionCanceledEventAttributes{
				WorkflowExecutionCanceledEventAttributes: &history.WorkflowExecutionCanceledEventAttributes{
					Details: &common.Payloads{
						Payloads: []*common.Payload{
							{
								Data: []byte(`{"key":"value"}`),
							},
						},
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_WORKFLOW_EXECUTION_CANCELED.String(), hist.EventType)
		require.Equal(t, map[string]any{constant.WorkflowDetails: []any{map[string]any{"key": "value"}}}, *hist.Attributes)
	})

	t.Run("Workflow Execution Terminated", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_WORKFLOW_EXECUTION_TERMINATED,
			Attributes: &history.HistoryEvent_WorkflowExecutionTerminatedEventAttributes{
				WorkflowExecutionTerminatedEventAttributes: &history.WorkflowExecutionTerminatedEventAttributes{
					Reason: "test terminate",
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_WORKFLOW_EXECUTION_TERMINATED.String(), hist.EventType)
		require.Equal(t, map[string]any{constant.WorkflowReason: "test terminate"}, *hist.Attributes)
	})

	t.Run("Activity Task Scheduled", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_ACTIVITY_TASK_SCHEDULED,
			Attributes: &history.HistoryEvent_ActivityTaskScheduledEventAttributes{
				ActivityTaskScheduledEventAttributes: &history.ActivityTaskScheduledEventAttributes{
					ActivityId: "SampleActivity",
					ActivityType: &common.ActivityType{
						Name: "SampleActivityType",
					},
					Input: &common.Payloads{
						Payloads: []*common.Payload{
							{
								Data: []byte(`{"key":"value"}`),
							},
						},
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_ACTIVITY_TASK_SCHEDULED.String(), hist.EventType)
		require.Equal(t, "SampleActivity", *hist.TaskName)
		require.Equal(t, "SampleActivityType", *hist.TaskType)
		require.Equal(t, map[string]any{constant.WorkflowInput: []any{map[string]any{"key": "value"}}}, *hist.Attributes)
	})

	t.Run("Activity Task Started event", func(t *testing.T) {
		taskName := "SampleActivity"
		taskType := "SampleActivityType"
		histEvents := []HistoryEvent{
			{
				EventId:  123,
				TaskName: &taskName,
				TaskType: &taskType,
			},
		}
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_ACTIVITY_TASK_STARTED,
			Attributes: &history.HistoryEvent_ActivityTaskStartedEventAttributes{
				ActivityTaskStartedEventAttributes: &history.ActivityTaskStartedEventAttributes{
					ScheduledEventId: 123,
					LastFailure: &failure.Failure{
						Message: "test fail message",
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, histEvents)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_ACTIVITY_TASK_STARTED.String(), hist.EventType)
		require.Equal(t, taskName, *hist.TaskName)
		require.Equal(t, taskType, *hist.TaskType)
		require.Equal(t, map[string]any{constant.WorkflowLastFailure: "test fail message"}, *hist.Attributes)
	})

	t.Run("Activity Task Completed", func(t *testing.T) {
		taskName := "SampleActivity"
		taskType := "SampleActivityType"
		histEvents := []HistoryEvent{
			{
				EventId:  123,
				TaskName: &taskName,
				TaskType: &taskType,
			},
		}
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_ACTIVITY_TASK_COMPLETED,
			Attributes: &history.HistoryEvent_ActivityTaskCompletedEventAttributes{
				ActivityTaskCompletedEventAttributes: &history.ActivityTaskCompletedEventAttributes{
					ScheduledEventId: 123,
					Result: &common.Payloads{
						Payloads: []*common.Payload{
							{
								Data: []byte(`{"key":"value"}`),
							},
						},
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, histEvents)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_ACTIVITY_TASK_COMPLETED.String(), hist.EventType)
		require.Equal(t, taskName, *hist.TaskName)
		require.Equal(t, taskType, *hist.TaskType)
		require.Equal(t, map[string]any{constant.WorkflowResult: []any{map[string]any{"key": "value"}}}, *hist.Attributes)
	})

	t.Run("Activity Task Canceled Requested", func(t *testing.T) {
		taskName := "SampleActivity"
		taskType := "SampleActivityType"
		histEvents := []HistoryEvent{
			{
				EventId:  123,
				TaskName: &taskName,
				TaskType: &taskType,
			},
		}
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_ACTIVITY_TASK_CANCEL_REQUESTED,
			Attributes: &history.HistoryEvent_ActivityTaskCancelRequestedEventAttributes{
				ActivityTaskCancelRequestedEventAttributes: &history.ActivityTaskCancelRequestedEventAttributes{
					ScheduledEventId: 123,
				},
			},
		}
		hist, err := getHistoryEvent(event, histEvents)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_ACTIVITY_TASK_CANCEL_REQUESTED.String(), hist.EventType)
		require.Equal(t, taskName, *hist.TaskName)
		require.Equal(t, taskType, *hist.TaskType)
	})

	t.Run("Activity Task Canceled", func(t *testing.T) {
		taskName := "SampleActivity"
		taskType := "SampleActivityType"
		histEvents := []HistoryEvent{
			{
				EventId:  123,
				TaskName: &taskName,
				TaskType: &taskType,
			},
		}
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_ACTIVITY_TASK_CANCELED,
			Attributes: &history.HistoryEvent_ActivityTaskCanceledEventAttributes{
				ActivityTaskCanceledEventAttributes: &history.ActivityTaskCanceledEventAttributes{
					ScheduledEventId: 123,
				},
			},
		}
		hist, err := getHistoryEvent(event, histEvents)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_ACTIVITY_TASK_CANCELED.String(), hist.EventType)
		require.Equal(t, taskName, *hist.TaskName)
		require.Equal(t, taskType, *hist.TaskType)
	})

	t.Run("Activity Task Failed", func(t *testing.T) {
		taskName := "SampleActivity"
		taskType := "SampleActivityType"
		histEvents := []HistoryEvent{
			{
				EventId:  123,
				TaskName: &taskName,
				TaskType: &taskType,
			},
		}
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_ACTIVITY_TASK_FAILED,
			Attributes: &history.HistoryEvent_ActivityTaskFailedEventAttributes{
				ActivityTaskFailedEventAttributes: &history.ActivityTaskFailedEventAttributes{
					ScheduledEventId: 123,
					Failure: &failure.Failure{
						Message: "test fail message",
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, histEvents)
		require.NoError(t, err)
		require.Equal(t, enums.EVENT_TYPE_ACTIVITY_TASK_FAILED.String(), hist.EventType)
		require.Equal(t, taskName, *hist.TaskName)
		require.Equal(t, taskType, *hist.TaskType)
		require.Equal(t, map[string]any{constant.WorkflowFailure: "test fail message"}, *hist.Attributes)
	})

	t.Run("Start Child Workflow Execution Initiated", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_START_CHILD_WORKFLOW_EXECUTION_INITIATED,
			Attributes: &history.HistoryEvent_StartChildWorkflowExecutionInitiatedEventAttributes{
				StartChildWorkflowExecutionInitiatedEventAttributes: &history.StartChildWorkflowExecutionInitiatedEventAttributes{
					Memo: &common.Memo{
						Fields: map[string]*common.Payload{
							"name": {
								Data: []byte(`"FormActivity"`),
							},
						},
					},
					WorkflowType: &common.WorkflowType{
						Name: "Form",
					},
					Input: &common.Payloads{
						Payloads: []*common.Payload{
							{
								Data: []byte(`{"key":"value"}`),
							},
						},
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Equal(t, "Form"+constant.WorkflowTaskScheduled, hist.EventType)
		require.Equal(t, "FormActivity", *hist.TaskName)
		require.Equal(t, "Form", *hist.TaskType)
		require.Equal(t, map[string]any{constant.WorkflowInput: []any{map[string]any{"key": "value"}}}, *hist.Attributes)
	})

	t.Run("Child Workflow Execution Started", func(t *testing.T) {
		taskName := "FormActivity"
		taskType := "FormActivityType"
		histEvents := []HistoryEvent{
			{
				EventId:  123,
				TaskName: &taskName,
				TaskType: &taskType,
			},
		}
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_STARTED,
			Attributes: &history.HistoryEvent_ChildWorkflowExecutionStartedEventAttributes{
				ChildWorkflowExecutionStartedEventAttributes: &history.ChildWorkflowExecutionStartedEventAttributes{
					InitiatedEventId: 123,
					WorkflowType: &common.WorkflowType{
						Name: "Form",
					},
					WorkflowExecution: &common.WorkflowExecution{
						WorkflowId: "12345",
						RunId:      "67890",
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, histEvents)
		require.NoError(t, err)
		require.Equal(t, "Form"+constant.WorkflowTaskStarted, hist.EventType)
		require.Equal(t, "FormActivity", *hist.TaskName)
		require.Equal(t, "Form", *hist.TaskType)
		require.Equal(t, map[string]any{"workflow_id": "12345", "workflow_run_id": "67890"}, *hist.Attributes)
	})

	t.Run("Child Workflow Execution Completed", func(t *testing.T) {
		taskName := "FormActivity"
		taskType := "FormActivityType"
		histEvents := []HistoryEvent{
			{
				EventId:  123,
				TaskName: &taskName,
				TaskType: &taskType,
			},
		}
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_COMPLETED,
			Attributes: &history.HistoryEvent_ChildWorkflowExecutionCompletedEventAttributes{
				ChildWorkflowExecutionCompletedEventAttributes: &history.ChildWorkflowExecutionCompletedEventAttributes{
					InitiatedEventId: 123,
					WorkflowType: &common.WorkflowType{
						Name: "Form",
					},
					Result: &common.Payloads{
						Payloads: []*common.Payload{
							{
								Data: []byte(`{"key":"value"}`),
							},
						},
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, histEvents)
		require.NoError(t, err)
		require.Equal(t, "Form"+constant.WorkflowTaskCompleted, hist.EventType)
		require.Equal(t, "FormActivity", *hist.TaskName)
		require.Equal(t, "Form", *hist.TaskType)
		require.Equal(t, map[string]any{constant.WorkflowResult: []any{map[string]any{"key": "value"}}}, *hist.Attributes)
	})

	t.Run("Child Workflow Execution Failed", func(t *testing.T) {
		taskName := "FormActivity"
		taskType := "FormActivityType"
		histEvents := []HistoryEvent{
			{
				EventId:  123,
				TaskName: &taskName,
				TaskType: &taskType,
			},
		}
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_FAILED,
			Attributes: &history.HistoryEvent_ChildWorkflowExecutionFailedEventAttributes{
				ChildWorkflowExecutionFailedEventAttributes: &history.ChildWorkflowExecutionFailedEventAttributes{
					InitiatedEventId: 123,
					WorkflowType: &common.WorkflowType{
						Name: "Form",
					},
					Failure: &failure.Failure{
						Message: "test fail message",
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, histEvents)
		require.NoError(t, err)
		require.Equal(t, "Form"+constant.WorkflowTaskFailed, hist.EventType)
		require.Equal(t, "FormActivity", *hist.TaskName)
		require.Equal(t, "Form", *hist.TaskType)
		require.Equal(t, map[string]interface{}{constant.WorkflowFailure: "test fail message"}, *hist.Attributes)
	})

	t.Run("Marker Recorded", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_MARKER_RECORDED,
			Attributes: &history.HistoryEvent_MarkerRecordedEventAttributes{
				MarkerRecordedEventAttributes: &history.MarkerRecordedEventAttributes{
					Details: map[string]*common.Payloads{"data": {Payloads: []*common.Payload{{Data: []byte(`{"sideEffectId":"End Event", "sideEffectType":"End"}`)}}}},
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Equal(t, "End Event", *hist.TaskName)
		require.Equal(t, "End", *hist.TaskType)
	})

	t.Run("test ignore event", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_UNSPECIFIED,
		}
		hist, err := getHistoryEvent(event, nil)
		require.NoError(t, err)
		require.Nil(t, hist)
	})

	t.Run("test error unmarshal payloads", func(t *testing.T) {
		event := &history.HistoryEvent{
			EventType: enums.EVENT_TYPE_ACTIVITY_TASK_COMPLETED,
			Attributes: &history.HistoryEvent_ActivityTaskCompletedEventAttributes{
				ActivityTaskCompletedEventAttributes: &history.ActivityTaskCompletedEventAttributes{
					Result: &common.Payloads{
						Payloads: []*common.Payload{
							{
								Data: []byte(``),
							},
						},
					},
				},
			},
		}
		hist, err := getHistoryEvent(event, nil)
		require.Error(t, err)
		require.NotNil(t, hist)
	})
}
