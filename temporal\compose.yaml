name: hcc3

services:
  # === Database ===
  postgresql:
    image: postgres:${POSTGRESQL_VERSION:-15}
    container_name: temporal-postgresql
    environment:
      POSTGRES_PASSWORD: temporal
      POSTGRES_USER: temporal
      POSTGRES_DB: temporal
    ports:
      - "7432:5432"
    volumes:
      - temporal_postgresql_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - temporal-network

  # === Temporal Server ===
  temporal:
    image: temporalio/auto-setup:${TEMPORAL_VERSION:-1.24.2}
    container_name: temporal-server
    depends_on:
      - postgresql
    environment:
      - DB=postgresql
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=postgresql
      - DYNAMIC_CONFIG_FILE_PATH=config/dynamicconfig/development-sql.yaml
      - ENABLE_ES=false
      - BIND_ON_IP=0.0.0.0
      - TEMPORAL_BROADCAST_ADDRESS=0.0.0.0
    ports:
      - "7233:7233"
    volumes:
      - ./dynamicconfig:/etc/temporal/config/dynamicconfig
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - temporal-network

  # === Temporal Admin Tools ===
  temporal-admin-tools:
    image: temporalio/admin-tools:${TEMPORAL_VERSION:-1.24.2}
    container_name: temporal-admin-tools
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CLI_ADDRESS=temporal:7233
    stdin_open: true
    tty: true
    restart: unless-stopped
    networks:
      - temporal-network

  # === Temporal Web UI ===
  temporal-ui:
    image: temporalio/ui:${TEMPORAL_UI_VERSION:-2.28.0}
    container_name: temporal-ui
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:3000,http://localhost:7070
      - TEMPORAL_NOTIFY_ON_NEW_VERSION=false
      - TEMPORAL_UI_PORT=8080
    ports:
      - "7070:8080"
    restart: unless-stopped
    networks:
      - temporal-network

# === Volumes ===
volumes:
  temporal_postgresql_data:
    driver: local

# === Networks ===
networks:
  temporal-network:
    driver: bridge
    name: temporal-network