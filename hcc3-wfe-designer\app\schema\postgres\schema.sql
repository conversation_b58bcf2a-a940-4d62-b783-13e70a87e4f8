-- Stores enum for BPMN event icon
CREATE TABLE enum_event_icon(
    id TEXT NOT NULL,
    comment TEXT,
    PRIMARY KEY (id)
);

INSERT INTO enum_event_icon(id, comment) VALUES
('none', NULL),
('message1', NULL),
('message2', NULL),
('timer1', NULL),
('conditional1', NULL),
('link1', NULL),
('link2', NULL),
('signal1', NULL),
('signal2', NULL),
('error1', NULL),
('error2', NULL),
('escalation1', NULL),
('escalation2', NULL),
('termination1', NULL),
('termination2', NULL),
('compensation1', NULL),
('compensation2', NULL),
('cancel1', NULL),
('cancel2', NULL),
('multiple1', NULL),
('multiple2', NULL),
('parallel1', NULL),
('parallel2', NULL);

-- Stores enum for BPMN activity icon
CREATE TABLE enum_activity_icon(
    id TEXT NOT NULL,
    comment TEXT,
    PRIMARY KEY (id)
);

INSERT INTO enum_activity_icon(id, comment) VALUES
('none', NULL),
('receive', NULL),
('send', NULL),
('business-rule', NULL),
('service', NULL),
('script', NULL),
('manual', NULL),
('user', NULL);

-- Stores service event type
CREATE TABLE service_event(
    id uuid NOT NULL,
    service_name TEXT NOT NULL,
    event_type TEXT NOT NULL,
    event_icon TEXT NOT NULL REFERENCES enum_event_icon ON DELETE RESTRICT ON UPDATE CASCADE,
    event_param JSONB NOT NULL,
    event_result JSONB NOT NULL,
    PRIMARY KEY (id),
    UNIQUE (event_type)
);


-- Stores service activity type
CREATE TABLE service_activity(
    id uuid NOT NULL,
    service_name TEXT NOT NULL,
    activity_type TEXT NOT NULL,
    activity_icon TEXT NOT NULL REFERENCES enum_activity_icon ON DELETE RESTRICT ON UPDATE CASCADE,
    activity_param JSONB NOT NULL,
    activity_result JSONB NOT NULL,
    timeout_in_second NUMERIC NOT NULL DEFAULT 300,
    PRIMARY KEY (id),
    UNIQUE (activity_type)
);

-- Stores form template
CREATE TABLE form_template(
    id uuid NOT NULL,
    name TEXT NOT NULL,
    form_json JSONB NOT NULL,
    component_keys JSONB NOT NULL,
    ---
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by TEXT NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_by TEXT NOT NULL,
    tenant_id TEXT NOT NULL,
    occ_lock INT NOT NULL DEFAULT 0,
    PRIMARY KEY (id),
    UNIQUE (name, tenant_id)
);

-- Stores workflow template
CREATE TABLE workflow_template
(
    id            uuid      NOT NULL,
    name          TEXT      NOT NULL,
    workflow_json JSONB     NOT NULL,
    designer_json JSONB     NOT NULL,
    editable      BOOLEAN   NOT NULL,
    ---
    created_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by    TEXT      NOT NULL,
    updated_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_by    TEXT      NOT NULL,
    tenant_id     TEXT      NOT NULL,
    occ_lock INT NOT NULL DEFAULT 0,
    PRIMARY KEY (id),
    UNIQUE (name, tenant_id)
);

-- Function to check occ lock before Update
CREATE OR REPLACE FUNCTION set_occ_lock()
    RETURNS TRIGGER
    LANGUAGE plpgsql AS
'
    BEGIN
        IF NEW.occ_lock <> OLD.occ_lock THEN
            RAISE EXCEPTION ''Occ Lock value does not match. Transaction rejected.'';
        END IF;
        NEW.occ_lock = NEW.occ_lock + 1;
        RETURN NEW;
    END
';

-- Function to set current timestamp before Update
CREATE OR REPLACE FUNCTION set_updated_at()
    RETURNS TRIGGER
    LANGUAGE plpgsql AS
'
    BEGIN
        NEW.updated_at = now();
        RETURN NEW;
    END
';

-- Set trigger for form_template table
CREATE OR REPLACE TRIGGER set_form_template_occ_lock
    BEFORE UPDATE ON form_template
    FOR EACH ROW
    EXECUTE FUNCTION set_occ_lock();

CREATE OR REPLACE TRIGGER set_form_template_updated_at
    BEFORE UPDATE ON form_template
    FOR EACH ROW
    EXECUTE FUNCTION set_updated_at();

-- Set trigger for workflow_template table
CREATE OR REPLACE TRIGGER set_workflow_template_occ_lock
    BEFORE UPDATE ON workflow_template
    FOR EACH ROW
    EXECUTE FUNCTION set_occ_lock();

CREATE OR REPLACE TRIGGER set_workflow_template_updated_at
    BEFORE UPDATE ON workflow_template
    FOR EACH ROW
    EXECUTE FUNCTION set_updated_at();
