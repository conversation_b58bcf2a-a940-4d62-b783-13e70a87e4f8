import type { dia, ui } from "rappid/rappid";
import { Start } from "$lib/aoh/wfd/bpmn/shapes";

export type ValidatorContext = {
	paper: dia.Paper;
	selection: ui.Selection;
};

const add = (ctx: ValidatorContext) => {
	// Check for duplicate start
	const validateStart = (_err: Error, command: { data: { id: string } }, next: (err?: Error) => void) => {
		const model = ctx.paper.getModelById(command.data.id);

		if (model instanceof Start) {
			const elements = model.graph.getElements();
			for (const element of elements) {
				if (element.id === model.id) {
					continue;
				}

				if (element.constructor === model.constructor) {
					ctx.selection.collection.add(element);
					return next(new Error());
				}
			}
		}

		return next();
	};

	return [validateStart];
};

export const validators = { add };
