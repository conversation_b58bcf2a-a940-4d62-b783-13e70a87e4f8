package ucs

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/stretchr/testify/assert"
	"go.temporal.io/sdk/testsuite"
)

func TestActivities_CreateUCSRoom(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		//nolint:errcheck
		rw.Write([]byte(`{"data":[{"id": "123", "name": "UCS Room"}]}`))
	}))
	defer server.Close()

	expected := "123"
	a := &Activities{
		createRoomApi: server.URL,
	}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.CreateUCSRoom, "UCS Room")
	assert.NoError(t, err)
	var result string
	_ = got.Get(&result)
	assert.Equal(t, expected, result)
}

func TestActivities_BroadcastUCSAnnouncement(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		rw.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	a := &Activities{
		broadcastRoomApi: server.URL,
	}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	_, err := env.ExecuteActivity(a.BroadcastUCSAnnouncement, "123", "UCS Room Announcement")
	assert.NoError(t, err)
}

func TestActivities_EndUCSChatRoom(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		rw.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	a := &Activities{
		endRoomApi: server.URL,
	}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	_, err := env.ExecuteActivity(a.EndUCSChatRoom, "123")
	assert.NoError(t, err)
}

func TestNew(t *testing.T) {
	assert.NotNil(t, New(config.Ucs{}))
}
