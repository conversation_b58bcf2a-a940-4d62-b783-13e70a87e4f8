<script lang="ts">
	import { onMount } from "svelte";
	import type { dia, ui } from "rappid/rappid";
	import * as Select from "$lib/aoh/wfd/components/ui/select";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { Flow, ActivityBase, Event } from "$lib/aoh/wfd/bpmn/shapes";

	let { inspector, path, resultPath }: { inspector: ui.Inspector; path: string; resultPath: string } = $props();

	const cell = inspector.options.cell as Flow;
	let listResult: Record<string, dia.Element> = {};
	let value: string = $state("");
	let element: ActivityBase;

	const traverseBfs = function (el: dia.Element): void {
		cell.graph.bfs(
			el,
			(el, distance) => {
				if (el instanceof ActivityBase && el.attr(resultPath) && distance) {
					listResult[el.id] = el;
				}
				if (el instanceof Event) {
					listResult[el.id] = el;
					const parent = el.getParentCell();
					if (parent) {
						traverseBfs(parent as dia.Element);
					}
				}
				return true;
			},
			{ inbound: true, deep: false }
		);
	};

	onMount(() => {
		value = cell.prop(path);
		const el = cell.isLink() ? cell.getSourceElement() : cell;

		if (el) {
			traverseBfs(el);
			delete listResult[el.id];
		}

		if (cell.isLink() && value && !listResult[value]) {
			const oldElement = cell.graph.getCell(value) as ActivityBase;
			oldElement.unsubscribe("attrs/data/result", cell.id);
			cell.updateConditionalLabel();
		}
	});

	const onChangeValue = () => {
		if (!cell.isLink()) {
			(cell as ActivityBase).prop(path, value);
			return;
		}
		const currentValue = cell.prop(path);
		if (currentValue) {
			const oldElement = cell.graph.getCell(currentValue) as ActivityBase;
			oldElement.unsubscribe("attrs/data/result", cell.id);
		}

		element = listResult[value] as ActivityBase;
		element.subscribe("attrs/data/result", cell.id);
		cell.prop(path, value);
		cell.updateConditionalLabel();
	};

	const updateCellValue = () => {
		// shad-cn popover closing animation is slower than jointJS inspector refresh
		// so we need to delay the applyChangeToCell function
		setTimeout(() => {
			onChangeValue();
		}, 200);
	};
	const triggerContent = $derived(listResult[value]?.attr(resultPath) ?? "Select a variable");
</script>

<Label>Variable Name</Label>
<Select.Root type="single" allowDeselect={false} bind:value onValueChange={updateCellValue}>
	<Select.Trigger class="w-full">
		{triggerContent}
	</Select.Trigger>
	<Select.Content>
		<Select.Group>
			{#each Object.keys(listResult) as item}
				<Select.Item value={item} label={listResult[item].attr(resultPath)}
					>{listResult[item].attr(resultPath)}</Select.Item
				>
			{/each}
		</Select.Group>
	</Select.Content>
</Select.Root>
