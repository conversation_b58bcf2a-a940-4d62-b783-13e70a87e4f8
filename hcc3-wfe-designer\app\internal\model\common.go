package model

import (
	"context"
	"fmt"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
	"go.uber.org/zap"
)

// Store contains common db operations
type Store struct {
	Db sqlplugin.DB
}

func NewStore(db sqlplugin.DB) Store {
	return Store{
		Db: db,
	}
}

func (s *Store) Close() {
	if s.Db != nil {
		err := s.Db.Close()
		if err != nil {
			aohlog.Error("failed to close db", zap.Error(err))
		}
	}
}

// txExecute performs a transactional database operation. If the operation
// returns an error, it will rollback the transaction and return an error.
// If the operation succeeds, it will commit the transaction and return nil.
func (s *Store) txExecute(ctx context.Context, operation string, f func(tx sqlplugin.Tx) error) error {
	tx, err := s.Db.BeginTx(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	if err = f(tx); err != nil {
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			aohlog.Error("transaction rollback error", zap.Error(rollbackErr))
		}
		return fmt.Errorf("%s failed: %w", operation, err)
	}
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	return nil
}
