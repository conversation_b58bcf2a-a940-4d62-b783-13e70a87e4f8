<script lang="ts">
	import { onMount } from "svelte";
	import { dia, ui, shapes } from "rappid/rappid";

	let { designerJson }: { designerJson: unknown } = $props();
	let container: HTMLElement = $state()!;

	let graph: dia.Graph = new dia.Graph({}, { cellNamespace: shapes });
	let paper: dia.Paper;
	let paperScroller: ui.PaperScroller;

	onMount(() => {
		paper = new dia.Paper({
			width: container.offsetWidth,
			height: container.offsetHeight,
			model: graph,
			interactive: {
				linkMove: false,
				elementMove: false,
			},
			cellViewNamespace: shapes,
			clickThreshold: 10,
			// Connections
			defaultConnectionPoint: {
				name: "boundary",
				args: { stroke: true },
			},
			defaultAnchor: {
				name: "perpendicular",
			},
		});

		//Paper Scroller
		paperScroller = new ui.PaperScroller({
			paper,
			autoResizePaper: true,
			cursor: "grab",
		});

		paper.on("blank:pointerdown", paperScroller.startPanning);
		container.appendChild(paperScroller.render().el);

		graph.clear();

		if (designerJson) {
			graph.fromJSON(designerJson);
		}

		paperScroller.centerContent();
	});
</script>

<div bind:this={container} class="max-h-[500px] overflow-auto"></div>
