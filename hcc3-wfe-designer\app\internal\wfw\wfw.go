package wfw

import (
	"context"
	"errors"
	"net"
	"net/http"
	"sync"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/aoh-golib/temporal"
	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/mssfoobar/app/wfe/internal/wfw/activities/demo"
	"github.com/mssfoobar/app/wfe/internal/wfw/activities/generic"
	"github.com/mssfoobar/app/wfe/internal/wfw/activities/sample"
	"github.com/mssfoobar/app/wfe/internal/wfw/activities/ucs"
	"github.com/mssfoobar/app/wfe/internal/wfw/handler"

	"go.temporal.io/sdk/worker"
	"go.uber.org/zap"
)

type WorkflowWorker struct {
	http *http.Server
	wg   sync.WaitGroup
}

func New() *WorkflowWorker {
	return &WorkflowWorker{}
}

// runWorker initialize temporal worker instance
func (wfw *WorkflowWorker) runWorker(c config.Option, interruptCh <-chan interface{}) error {
	client, err := temporal.NewClient(temporal.Config{
		HostPort:  c.Temporal.Host + ":" + c.Temporal.Port,
		Namespace: c.Temporal.Namespace,
	})
	if err != nil {
		return err
	}
	defer client.Close()

	w := worker.New(client, c.Temporal.TaskQueue, worker.Options{
		DisableWorkflowWorker: true,
	})

	w.RegisterActivity(&sample.Activities{})
	w.RegisterActivity(generic.New(generic.Option{
		SMTP: c.Smtp,
		GPT:  c.ChatGpt,
		IAN:  c.Ian,
	}))
	w.RegisterActivity(demo.New(demo.Option{
		GPT:    c.ChatGpt,
		IMS:    c.Ims,
		Hasura: c.Hasura,
	}))
	w.RegisterActivity(ucs.New(c.Ucs))

	return w.Run(interruptCh)
}

func (wfw *WorkflowWorker) Start(c config.Option) {
	interruptCh := make(chan interface{})
	wfw.http = &http.Server{
		Addr:    net.JoinHostPort("0.0.0.0", c.Port),
		Handler: handler.NewHealthRouter(),
	}

	aohlog.Info("[INIT] http server starting", zap.String("port", c.Port))
	wfw.wg.Add(1)
	go func() {
		defer wfw.wg.Done()
		if err := wfw.http.ListenAndServe(); !errors.Is(err, http.ErrServerClosed) {
			aohlog.Fatal("unable to start http server", zap.Error(err))
		}
		aohlog.Info("[SHUTDOWN] http server shutdown")
		interruptCh <- struct{}{}
	}()

	wfw.wg.Add(1)
	go func() {
		defer wfw.wg.Done()
		if err := wfw.runWorker(c, interruptCh); err != nil {
			aohlog.Fatal("unable to start temporal worker", zap.Error(err))
		}
		aohlog.Info("[SHUTDOWN] temporal worker shutdown")
	}()
}

// Stop gracefully exit the app
func (wfw *WorkflowWorker) Stop() {
	aohlog.Info("[SHUTDOWN] workflow worker shutting down")
	err := wfw.http.Shutdown(context.Background())
	if err != nil {
		aohlog.Error("[SHUTDOWN] http server shutdown error", zap.Error(err))
	}
	wfw.wg.Wait()
	aohlog.Info("[SHUTDOWN] workflow worker shutdown successfully")
}
