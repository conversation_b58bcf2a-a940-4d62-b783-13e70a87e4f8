<script lang="ts">
	import { ActivityBase } from "$lib/aoh/wfd/bpmn/shapes";
	import { onMount } from "svelte";
	import { oneDark } from "$lib/aoh/wfd/codemirror/theme";
	import { expression } from "$lib/aoh/wfd/codemirror";
	import { highlightActiveLine, highlightActiveLineGutter, highlightSpecialChars, keymap } from "@codemirror/view";
	import { bracketMatching, defaultHighlightStyle, syntaxHighlighting, syntaxTree } from "@codemirror/language";
	import {
		autocompletion,
		closeBrackets,
		type CompletionContext,
		completionKeymap,
		type CompletionResult,
	} from "@codemirror/autocomplete";
	import { defaultKeymap } from "@codemirror/commands";
	import CodeMirror from "svelte-codemirror-editor";
	import { EditorView } from "codemirror";
	import * as cmlint from "@codemirror/lint";
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { InternalActivity } from "$lib/aoh/wfd/activitySDK/private/activity";
	import {
		type ValidateConditionFromSchema,
		type ValidateResponse,
		validate_condition_schema,
		traverseBfs,
		getEndIndex,
		removeDuplicates,
	} from "$lib/aoh/wfd/components/inspector/conditional/ConditionField.svelte";
	import { superForm } from "sveltekit-superforms";
	import { zodClient } from "sveltekit-superforms/adapters";

	let { activity, name }: { activity: Activity; name: string } = $props();
	let activityInternal = activity as InternalActivity;

	let inspector = activityInternal._inspector;
	let path = "attrs/data/typeOptions/" + name;

	const cell = inspector.options.cell as ActivityBase;
	// remove prefix '=' on variable init
	let value: string = $state((cell.prop(path) || cell.prop(path + "Tmp") || "").replace(/^=/, ""));
	let validateStatus: ValidateResponse | null = $state(cell.prop(path + "Validate") || {});
	let completionSource: string[] = [];
	let variables: Record<string, unknown> = {};
	let codeMirrorContainer: HTMLDivElement;

	const super_validate_expression_form = superForm<ValidateConditionFromSchema>(
		{
			expression: "",
			variables: {},
		},
		{
			dataType: "json",
			validators: zodClient(validate_condition_schema),
			onSubmit: () => {
				$validate_expression_form = {
					expression: value,
					variables: variables,
				};
			},
			onResult: ({ result }) => {
				if (result.type === "success" && result.data) {
					validateStatus = result.data.validate;
					cell.removeProp(path + "Validate");
					cell.prop(path + "Validate", validateStatus);
					cell.prop(path + "Tmp", value);

					if (!validateStatus?.errors) {
						cell.prop(path, "=" + value);
					} else {
						cell.removeProp(path);
					}
				}
			},
		}
	);

	const { form: validate_expression_form, enhance: validate_expression_enhance } = super_validate_expression_form;

	const codemirrorCompletion = (ctx: CompletionContext): CompletionResult | null => {
		let nodeBefore = syntaxTree(ctx.state).resolveInner(ctx.pos, -1);

		if (nodeBefore.type.name !== "Identifier") {
			return null;
		}

		let textBefore = ctx.state.sliceDoc(nodeBefore.from, ctx.pos);

		let completionList: string[] = completionSource;
		// On explicit call, show all completion
		if (!ctx.explicit) {
			completionList = completionList.filter((source) => source?.startsWith(textBefore));
		}

		return {
			from: nodeBefore.from,
			options: completionList.map((source) => {
				const label = source;
				return {
					label,
					type: "variable",
				};
			}),
		};
	};

	onMount(() => {
		const el = cell.isLink() ? cell.getSourceElement() : cell;

		if (el) {
			traverseBfs(el, cell, completionSource, variables);
		}

		const codeMirrorInstance = EditorView.findFromDOM(codeMirrorContainer);

		if (codeMirrorInstance) {
			codeMirrorInstance.contentDOM.onblur = () => {
				applyChangeToCell();
			};
			codeMirrorInstance.contentDOM.onfocus = () => {
				validateStatus = {};
			};
		}
	});

	const applyChangeToCell = async () => {
		if (value) {
			super_validate_expression_form.submit(document.getElementById("validate_expression_form"));
		} else {
			validateStatus = {};
			cell.removeProp(path);
			cell.removeProp(path + "Tmp");
			cell.removeProp(path + "Validate");
		}
	};

	const errorLinter = cmlint.linter((view) => {
		let diagnostics: cmlint.Diagnostic[] = [];
		syntaxTree(view.state)
			.cursor()
			.iterate((_node) => {
				if (validateStatus?.data) {
					const fromIndex = validateStatus.data[0].Column;
					const toIndex = getEndIndex(value, fromIndex);
					if (toIndex !== -1) {
						diagnostics.push({
							from: fromIndex,
							to: toIndex,
							severity: "error",
							message: validateStatus.data[0].Message,
						});
					} else {
						diagnostics = [];
					}
				} else {
					diagnostics = [];
				}
			});
		return removeDuplicates(diagnostics, "message");
	});
</script>

<form id="validate_expression_form" method="POST" action="?/validate_expression" use:validate_expression_enhance></form>
<div bind:this={codeMirrorContainer} class="py-2">
	<div class="flex">
		<CodeMirror
			class="text-sm normal-case flex-auto"
			bind:value
			basic={false}
			theme={oneDark}
			extensions={[
				expression(),
				highlightSpecialChars(),
				syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
				highlightActiveLine(),
				highlightActiveLineGutter(),
				autocompletion({
					activateOnTyping: true,
					override: [codemirrorCompletion],
				}),
				bracketMatching(),
				closeBrackets(),
				keymap.of([...defaultKeymap, ...completionKeymap]),
				errorLinter,
			]}
		/>
	</div>

	{#if validateStatus?.errors}
		<div class="text-destructive text-xs normal-case whitespace-normal">
			${validateStatus.errors[0]?.message}
		</div>
	{/if}
</div>
