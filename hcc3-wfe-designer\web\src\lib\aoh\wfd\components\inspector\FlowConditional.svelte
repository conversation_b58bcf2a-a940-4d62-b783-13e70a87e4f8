<script lang="ts">
	import { onMount } from "svelte";
	import { ui } from "rappid/rappid";
	import * as Tabs from "$lib/aoh/wfd/components/ui/tabs";
	import * as Card from "$lib/aoh/wfd/components/ui/card";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import VariableField from "$lib/aoh/wfd/components/inspector/conditional/VariableField.svelte";
	import OperatorField from "$lib/aoh/wfd/components/inspector/conditional/OperatorField.svelte";
	import ValueField from "$lib/aoh/wfd/components/inspector/conditional/ValueField.svelte";
	import ConditionField from "$lib/aoh/wfd/components/inspector/conditional/ConditionField.svelte";
	import type { Flow } from "$lib/aoh/wfd/bpmn/shapes";

	let { inspector, path, resultPath }: { inspector: ui.Inspector; path: string; resultPath: string } = $props();
	const cell = inspector.options.cell as Flow;

	let value: string = $state("");

	onMount(() => {
		value = cell.prop(path) || "Basic";
	});

	const onChangeValue = () => {
		cell.prop(path, value);
	};
</script>

<Label>Condition</Label>
<Tabs.Root bind:value class="w-full" onValueChange={onChangeValue}>
	<Tabs.List class="grid w-full grid-cols-2">
		<Tabs.Trigger value="Basic">Basic</Tabs.Trigger>
		<Tabs.Trigger value="Advanced">Advanced</Tabs.Trigger>
	</Tabs.List>
	<Tabs.Content value="Basic">
		<Card.Root>
			<Card.Content class="p-4">
				<VariableField {inspector} path={"attrs/condition/variable"} {resultPath} />
				<OperatorField {inspector} path={"attrs/condition/operator"} />
				<ValueField {inspector} path={"attrs/condition/value"} />
			</Card.Content>
		</Card.Root>
	</Tabs.Content>
	<Tabs.Content value="Advanced">
		<Card.Root>
			<Card.Content class="p-4">
				<ConditionField {inspector} path={"attrs/condition/advanced"} />
			</Card.Content>
		</Card.Root>
	</Tabs.Content>
</Tabs.Root>
