package dsl

import (
	"strings"
)

type FormActivity struct {
	Name   string         `json:"name,omitempty"`
	Schema FormSchema     `json:"formSchema,omitempty"`
	Input  map[string]any `json:"formInput,omitempty"`
}

// FormSchema is the representation of the form-js schema
type (
	FormSchema struct {
		Type          string      `json:"type,omitempty"`
		Id            string      `json:"id,omitempty"`
		Exporter      Exporter    `json:"exporter,omitempty"`
		SchemaVersion int         `json:"schemaVersion,omitempty"`
		Components    []Component `json:"components,omitempty"`
	}

	Exporter struct {
		Name    string `json:"name,omitempty"`
		Version string `json:"version,omitempty"`
	}

	Component struct {
		Id                    string   `json:"id,omitempty"`
		Description           string   `json:"description,omitempty"`
		Action                string   `json:"action,omitempty"`
		Label                 string   `json:"label,omitempty"`
		Type                  string   `json:"type,omitempty"`
		DefaultValue          string   `json:"defaultValue,omitempty"`
		Readonly              bool     `json:"readonly,omitempty"`
		Text                  string   `json:"text,omitempty"`
		Validate              Validate `json:"validate,omitempty"`
		Layout                Layout   `json:"layout,omitempty"`
		Key                   string   `json:"key,omitempty"`
		ValuesKey             string   `json:"valuesKey,omitempty"`
		Values                []Value  `json:"values,omitempty"`
		DataSource            string   `json:"dataSource,omitempty"`
		Source                string   `json:"source,omitempty"`
		Alt                   string   `json:"alt,omitempty"`
		Subtype               string   `json:"subtype,omitempty"`
		DateLabel             string   `json:"dateLabel,omitempty"`
		TimeLabel             string   `json:"timeLabel,omitempty"`
		TimeSerializingFormat string   `json:"timeSerializingFormat,omitempty"`
		TimeInterval          int      `json:"timeInterval,omitempty"`
		Use24h                bool     `json:"use24h,omitempty"`
	}

	Layout struct {
		Row     string `json:"row,omitempty"`
		Columns int    `json:"columns,omitempty"`
	}

	Value struct {
		Label string `json:"label,omitempty"`
		Value string `json:"value,omitempty"`
	}

	Validate struct {
		ValidationType string `json:"validationType,omitempty"`
		Required       bool   `json:"required,omitempty"`
		Min            int    `json:"min,omitempty"`
		Max            int    `json:"max,omitempty"`
		Patterns       string `json:"patterns,omitempty"`
	}

	Conditional struct {
		Hide string `json:"hide,omitempty"`
	}
)

// bindData bind form data by creating reference key for each form component.
// Reference keys are started with '=' symbol and followed by component id or key.
//
// Example: =table, =checklist, =radio, =select
func (f *FormSchema) bindData(input map[string]any) {
	for i := 0; i < len(f.Components); i++ {
		switch f.Components[i].Type {
		case FormTable:
			if _, ok := input[f.Components[i].Id]; ok {
				f.Components[i].DataSource = "=" + f.Components[i].Id
			}
		case FormCheckList, FormRadio, FormSelect, FormTagList:
			if _, ok := input[f.Components[i].Key]; ok {
				f.Components[i].ValuesKey = "=" + f.Components[i].Key
				f.Components[i].Values = []Value{}
				input[f.Components[i].ValuesKey] = input[f.Components[i].Key]
				delete(input, f.Components[i].Key)
			}
		}
	}
}

// createFormInput return a map of bound form component key and value.
// Created map is used as form-js input object to fill the form input fields items
// (such as text, checkbox, etc).
func createFormInput(name string, values []any, arguments []string) map[string]any {
	input := make(map[string]any)
	for i, arg := range arguments {
		key := strings.TrimPrefix(arg, name+"_")
		value := values[i]
		if strList, ok := anyToStringList(value); ok {
			value = createSelectionInput(strList)
		}
		input[key] = value
	}
	return input
}

// anyToStringList is a helper function that convert any type to string list
func anyToStringList(v any) ([]string, bool) {
	var list []string
	if v == nil {
		return list, false
	}
	if _, ok := v.([]any); !ok {
		return list, false
	}
	for _, v := range v.([]any) {
		if _, ok := v.(string); !ok {
			return list, false
		}
		list = append(list, v.(string))
	}
	return list, true
}

// createSelectionInput is a helper function that convert string list to Value list
func createSelectionInput(s []string) []Value {
	var input []Value
	for _, v := range s {
		input = append(input, Value{Label: v, Value: v})
	}
	return input
}
