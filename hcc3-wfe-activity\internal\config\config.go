package config

import (
	"errors"
	"os"

	"github.com/caarlos0/env/v10"
	"github.com/joho/godotenv"
)

type (
	Option struct {
		Port     string `env:"APP_PORT" envDefault:"5000"`
		LogLevel string `env:"LOG_LEVEL" envDefault:"info"`
		Temporal `envPrefix:"TEMPORAL_"`
	}

	Temporal struct {
		Host              string `env:"HOST" envDefault:"localhost"`
		Port              string `env:"PORT" envDefault:"7233"`
		Namespace         string `env:"NAMESPACE" envDefault:"default"`
		WorkflowType      string `env:"WORKFLOW_TYPE" envDefault:"aoh_wfe"`
		WorkflowTaskQueue string `env:"WORKFLOW_TASKQUEUE" envDefault:"aoh_workflow"`
		ActivityTaskQueue string `env:"ACTIVITY_TASKQUEUE" envDefault:"aoh_activity"`
		TaskQueue string `env:"TASKQUEUE" envDefault:"taskqueue"`
	}
)

var AppConfig *Option

// Init load env from file into os and parse env to AppConfig
func Init(file string) (err error) {
	opt := Option{}
	if err = godotenv.Load(file); err != nil && !errors.Is(err, os.ErrNotExist) {
		return err
	}
	if err = env.Parse(&opt); err != nil {
		return err
	}
	AppConfig = &opt
	return nil
}
