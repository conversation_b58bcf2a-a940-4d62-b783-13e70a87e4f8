/**
 * Messages can be strings or anything supported by the structured clone algorithm (Strings, Objects, Arrays, Blobs,
 * ArrayBuffer, Map). This is actually based on the underlying implementation of the browser's BroadcastChannel API.
 *
 * The implementation may be switched out in the future if necessary, as this is essentially a just a BroadcastChannel
 * API wrapper.
 *
 * Same-origin only.
 */

import { browser } from "$app/environment";
// import { log } from "$lib/aoh/core/logger/Logger";
import type pino from "pino";
import { get, readable, type Readable } from "svelte/store";

/**
 * Add your channels here
 */

export enum Channel {
	AOH_WIDGET_RESIZED = "AOH_WIDGET_RESIZED",
}

export interface BroadcastMessage {
	id?: string;
	type: Channel;
}

/**
 * This message is broadcast whenever a widget is resized.
 *
 * Note: This code should be removed after the dashboard extends the BroadcastMessage within its own repository.
 * `browser_broadcaster` is better suited as a library.
 */
export interface MESSAGE_AOH_WIDGET_RESIZED extends BroadcastMessage {
	type: Channel.AOH_WIDGET_RESIZED;
	data: string;
}

/**
 * Type T must be anything supported by the structured clone algorithm (Strings, Objects, Arrays, Blobs, ArrayBuffer,
 * Map)
 */
export type Message = MESSAGE_AOH_WIDGET_RESIZED;

type BrowserBroadcasterChannel = Map<Channel, { sender?: BroadcastChannel; receiver?: BroadcastChannel }>;
type ListenerCallback = (message: Message) => void;
type AbortControllerMap = Map<symbol, AbortController>;

class BrowserBroadcaster {
	log: pino.Logger;
	channels: BrowserBroadcasterChannel;
	abortControllers: AbortControllerMap;

	/**
	 * Listens to all channels, including your own
	 * @param chan The channel to subscribe to
	 * @param callback A callback function to run when something is published on that channel
	 * @param symbol A symbol used to track the listener - you must use this symbol to unsubscribe if you wish to unsub
	 */
	public sub(chan: Channel, callback: ListenerCallback, symbol?: symbol) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		if (!this.channels.has(chan)) {
			this.channels.set(chan, { receiver: new BroadcastChannel(chan) });
		} else if (!this.channels.get(chan)!.receiver) {
			this.channels.get(chan)!.receiver = new BroadcastChannel(chan);
		}

		const listener = (event: MessageEvent) => {
			callback(event.data);
		};

		let options;

		if (symbol) {
			const abortController = new AbortController();

			this.abortControllers.set(symbol, abortController);

			options = {
				signal: abortController.signal,
			};
		}

		this.channels.get(chan)!.receiver!.addEventListener("message", listener, options);
	}

	/**
	 * Listens to all channels, excluding one with a specific id - this is useful if you need to exclude specific
	 * components from being subscribed to (e.g. yourself only)
	 * @param chan The channel to subscribe to
	 * @param id The 'id' of the message to subscribe to
	 * @param callback A callback function to run when something is published on that channel
	 * @param symbol A symbol used to track the listener - you must use this symbol to unsubscribe if you wish to unsub
	 */
	public sub_exclude(chan: Channel, id: string, callback: ListenerCallback, symbol?: symbol) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		if (!this.channels.has(chan)) {
			this.channels.set(chan, { receiver: new BroadcastChannel(chan) });
		} else if (!this.channels.get(chan)!.receiver) {
			this.channels.get(chan)!.receiver = new BroadcastChannel(chan);
		}

		const listener = (event: MessageEvent) => {
			if ((event.data as Message).id === id) {
				this.log.debug({ id, chan, event }, "Ignoring message due to sub_exclude exclusion.");
			} else {
				callback(event.data);
			}
		};

		let options;

		if (symbol) {
			const abortController = new AbortController();

			this.abortControllers.set(symbol, abortController);

			options = {
				signal: abortController.signal,
			};
		}

		this.channels.get(chan)!.receiver!.addEventListener("message", listener, options);
	}

	/**
	 * Listens to only the channel specified in the id
	 * @param chan The channel to subscribe to
	 * @param id The 'id' of the channel to subscribe to
	 * @param callback A callback function to run when something is published on that channel
	 * @param symbol A symbol used to track the listener - you must use this symbol to unsubscribe if you wish to unsub
	 */
	public sub_only(chan: Channel, id: string, callback: ListenerCallback, symbol?: symbol) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		if (!this.channels.has(chan)) {
			this.channels.set(chan, { receiver: new BroadcastChannel(chan) });
		} else if (!this.channels.get(chan)!.receiver) {
			this.channels.get(chan)!.receiver = new BroadcastChannel(chan);
		}

		const listener = (event: MessageEvent) => {
			if ((event.data as Message).id === id) {
				callback(event.data);
			} else {
				this.log.debug({ id, chan, event }, "Ignoring message due to sub_only exclusion.");
			}
		};

		let options;

		if (symbol) {
			const abortController = new AbortController();

			this.abortControllers.set(symbol, abortController);

			options = {
				signal: abortController.signal,
			};
		}

		this.channels.get(chan)!.receiver!.addEventListener("message", listener, options);
	}

	/**
	 * Listens to all external channels - channels that are not on the same browser tab.
	 * @param chan The channel to subscribe to
	 * @param callback A callback function to run when something is published on that channel.
	 */
	public sub_external(chan: Channel, callback: ListenerCallback, symbol?: symbol) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		/**
		 * We use the sender to subscribe so that we ignore broadcasts on the same channel
		 */
		if (!this.channels.has(chan)) {
			this.channels.set(chan, { sender: new BroadcastChannel(chan) });
		} else if (!this.channels.get(chan)!.sender) {
			this.channels.get(chan)!.sender = new BroadcastChannel(chan);
		}

		const listener = (event: MessageEvent) => {
			callback(event.data);
		};

		let options;

		if (symbol) {
			const abortController = new AbortController();

			this.abortControllers.set(symbol, abortController);

			options = {
				signal: abortController.signal,
			};
		}

		this.channels.get(chan)!.sender!.addEventListener("message", listener, options);
	}

	/**
	 * Listens to all external channels except for messages with given id
	 * @param chan The channel to subscribe to
	 * @param id The 'id' of the messages to exclude
	 * @param callback A callback function to run when something is published on that channel.
	 */
	public sub_external_exclude(chan: Channel, id: string, callback: ListenerCallback, symbol?: symbol) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		/**
		 * We use the sender to subscribe so that we ignore broadcasts on the same channel
		 */
		if (!this.channels.has(chan)) {
			this.channels.set(chan, { sender: new BroadcastChannel(chan) });
		} else if (!this.channels.get(chan)!.sender) {
			this.channels.get(chan)!.sender = new BroadcastChannel(chan);
		}

		const listener = (event: MessageEvent) => {
			if ((event.data as Message).id === id) {
				this.log.debug({ id, chan, event }, "Ignoring message due to exclusion.");
			} else {
				callback(event.data);
			}
		};

		let options;

		if (symbol) {
			const abortController = new AbortController();

			this.abortControllers.set(symbol, abortController);

			options = {
				signal: abortController.signal,
			};
		}

		this.channels.get(chan)!.sender!.addEventListener("message", listener, options);
	}

	/**
	 * Listens to only external channels with messages of a specific id
	 * @param chan The channel to subscribe to
	 * @param id The 'id' of the message to subscribe to
	 * @param callback A callback function to run when something is published on that channel.
	 */
	public sub_external_only(chan: Channel, id: string, callback: ListenerCallback, symbol?: symbol) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		/**
		 * We use the sender to subscribe so that we ignore broadcasts on the same channel
		 */
		if (!this.channels.has(chan)) {
			this.channels.set(chan, { sender: new BroadcastChannel(chan) });
		} else if (!this.channels.get(chan)!.sender) {
			this.channels.get(chan)!.sender = new BroadcastChannel(chan);
		}

		const listener = (event: MessageEvent) => {
			if ((event.data as Message).id === id) {
				callback(event.data);
			} else {
				this.log.debug({ id, chan, event }, "Ignoring message due to exclusion.");
			}
		};

		let options;

		if (symbol) {
			const abortController = new AbortController();

			this.abortControllers.set(symbol, abortController);

			options = {
				signal: abortController.signal,
			};
		}

		this.channels.get(chan)!.sender!.addEventListener("message", listener, options);
	}

	/**
	 * Publish a message to a chanel. You must pass an 'id' to message if you wish to selectively subscribe to certain
	 * messages in a channel.
	 * @param chan The channel to subscribe to
	 * @param callback A callback function to run when something is published on that channel.
	 */
	public pub(chan: Channel, message: Message) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		if (!this.channels.has(chan)) {
			this.channels.set(chan, { sender: new BroadcastChannel(chan) });
		} else if (!this.channels.get(chan)!.sender) {
			this.channels.get(chan)!.sender = new BroadcastChannel(chan);
		}

		this.channels.get(chan)!.sender!.postMessage(message);
	}

	/**
	 * Closes a channel for ALL users
	 * @param chan The channel to close
	 */
	public close(chan: Channel) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		if (this.channels.has(chan)) {
			this.channels.get(chan)?.receiver?.close();
		}

		this.channels.delete(chan);
	}

	/**
	 * Remove an event listener for a specific channel
	 * @param chan The channel to unsubscribe to
	 * @param symbol The symbol used to subscribe to the channel when attaching the event listener
	 */
	public unsub(chan: Channel, symbol: symbol) {
		if (!browser) {
			this.log.warn({ chan }, "Brower Broadcaster can only be used in the browser.");
			return;
		}

		if (!this.channels.has(chan)) {
			this.log.warn({ chan }, "Failed to unsub: Channel to unsub does not exist.");
			return;
		}

		if (!symbol) {
			this.log.warn({ chan, symbol }, "No provided symbol.");
			return;
		}

		const abcSymbol = this.abortControllers.get(symbol);

		if (!abcSymbol) {
			this.log.warn({ chan, symbol }, "no abort controller to unsubscribe from");
			return;
		}

		abcSymbol.abort();
	}

	constructor() {
		this.channels = new Map<Channel, { sender?: BroadcastChannel; receiver?: BroadcastChannel }>();
		// this.log = log.child({ src: "Brower Broadcaster" });
		this.abortControllers = new Map<symbol, AbortController>();
	}
}

const broadcasterStore: Readable<BrowserBroadcaster> = readable<BrowserBroadcaster>(new BrowserBroadcaster());

export const browserBroadcaster = get(broadcasterStore);
