//go:generate mockgen -source=interface.go -destination=mock/temporal_mock.go -package=mock
package temporal

import (
	"context"
	"encoding/json"
)

type Service interface {
	ExecuteWorkflow(ctx context.Context, workflowSchema json.RawMessage, metadata map[string]any) (*WorkflowRun, error)
	TerminateWorkflow(ctx context.Context, workflowId, reason string) error
	SignalWorkflow(ctx context.Context, workflowId, signalName string, data any) error
	ListOpenWorkflow(ctx context.Context, page, size int, asc bool) ([]WorkflowExecution, int, error)
	GetWorkflowHistory(ctx context.Context, id string, page, size int, desc bool) ([]HistoryEvent, int, error)
}

type WorkflowRun struct {
	WorkflowId string `json:"workflow_id"`
}

type WorkflowExecution struct {
	WorkflowId string `json:"workflow_id"`
	StartTime  string `json:"start_time"`
}

type HistoryEvent struct {
	EventId    int64                   `json:"-"`
	EventType  string                  `json:"event_type"`
	Timestamp  string                  `json:"timestamp"`
	TaskName   *string                 `json:"task_name,omitempty"`
	TaskType   *string                 `json:"task_type,omitempty"`
	Attributes *map[string]interface{} `json:"attributes,omitempty"`
}
