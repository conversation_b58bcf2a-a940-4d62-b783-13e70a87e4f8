package generic

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/stretchr/testify/assert"
	"go.temporal.io/sdk/testsuite"
)

func TestActivities_Timer(t *testing.T) {
	a := &Activities{}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	_, err := env.ExecuteActivity(a.Timer, "PT1S")
	assert.NoError(t, err)
}

func TestActivities_Delay(t *testing.T) {
	a := &Activities{}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	_, err := env.ExecuteActivity(a.Delay, 1.23)
	assert.NoError(t, err)
}

func TestActivities_HttpCall(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		//nolint:errcheck
		rw.Write([]byte(`{"key":"value"}`))
	}))
	defer server.Close()

	expected := map[string]any{"key": "value"}
	a := &Activities{}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.HttpCall, server.URL, http.MethodGet, nil)
	assert.NoError(t, err)
	var result map[string]any
	_ = got.Get(&result)
	assert.Equal(t, expected, result)
}

type mockSmtp struct {
	SendMailFunc func(to []string, msg []byte) error
}

func (s *mockSmtp) SendMail(to []string, msg []byte) error {
	return s.SendMailFunc(to, msg)
}

func TestActivities_SendEmail(t *testing.T) {
	a := &Activities{
		smtp: &mockSmtp{SendMailFunc: func(to []string, msg []byte) error {
			return nil
		}},
	}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	_, err := env.ExecuteActivity(a.SendEmail, "<EMAIL>", "subject", "mailBody")
	assert.NoError(t, err)
}

func TestActivities_AskChatGPT(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		//nolint:errcheck
		rw.Write([]byte(`{"choices":[{"message":{"content":"hello"}}]}`))
	}))
	defer server.Close()

	expected := "hello"
	a := &Activities{
		aiModel: &ChatGPT{
			config.ChatGpt{
				Url:    server.URL,
				ApiKey: "",
			},
		},
	}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.AskChatGPT, expected)
	assert.NoError(t, err)
	var result string
	_ = got.Get(&result)
	assert.Equal(t, expected, result)
}

func TestNew(t *testing.T) {
	assert.NotNil(t, New(Option{}))
}

// Testing
func TestActivities_InAppNotification(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		//nolint:errcheck
		rw.Write([]byte(`{"key":"value"}`))
	}))
	defer server.Close()

	expected := map[string]any{"key": "value"}
	a := &Activities{
		ian: &InAppImpl{
			endpoint: server.URL,
		},
	}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.InAppNotification, "senderId", "receiverId", "tenantId", "title", "body")
	assert.NoError(t, err)
	var result map[string]any
	_ = got.Get(&result)
	assert.Equal(t, expected, result)
}
