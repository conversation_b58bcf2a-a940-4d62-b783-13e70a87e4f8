{"definitions": {"activityInvocation": {"properties": {"id": {"type": "string"}, "type": {"type": "string"}, "arguments": {"elements": {"type": "string"}}, "result": {"type": "string"}, "boundaryEvents": {"elements": {"type": "string"}}}, "optionalProperties": {"options": {"optionalProperties": {"startToCloseTimeout": {"type": "uint32"}}}}}, "callActivity": {"properties": {"id": {"type": "string"}, "type": {"type": "string"}, "result": {"type": "string"}, "arguments": {"elements": {"type": "string"}}, "boundaryEvents": {"elements": {"type": "string"}}}}, "form": {"properties": {"id": {"type": "string"}, "type": {"type": "string"}, "result": {"type": "string"}, "arguments": {"elements": {"type": "string"}}, "boundaryEvents": {"elements": {"type": "string"}}}}, "event": {"properties": {"id": {"type": "string"}, "type": {"type": "string"}, "interrupting": {"type": "boolean"}, "result": {"type": "string"}, "arguments": {"elements": {"type": "string"}}}}, "end": {"properties": {"id": {"type": "string"}, "terminate": {"type": "boolean"}}}, "conditional": {"optionalProperties": {"basic": {"properties": {"operator": {"enum": ["EQ", "NEQ", "MT", "LT", "MTE", "LTE"]}, "input": {"type": "string"}, "value": {}}}, "advance": {"properties": {"expression": {"type": "string"}}}}}, "statement": {"properties": {"name": {"type": "string"}}, "optionalProperties": {"form": {"ref": "form", "nullable": true}, "activity": {"ref": "activityInvocation", "nullable": true}, "callActivity": {"ref": "callActivity", "nullable": true}, "event": {"ref": "event", "nullable": true}, "end": {"ref": "end", "nullable": true}, "parallel": {"ref": "parallel", "nullable": true}, "switch": {"ref": "switch", "nullable": true}, "next": {"type": "string"}}}, "parallel": {"properties": {"id": {"type": "string"}, "branches": {"elements": {"optionalProperties": {"next": {"type": "string"}}}}}}, "switch": {"optionalProperties": {"default": {"optionalProperties": {"next": {"type": "string"}}}}, "properties": {"id": {"type": "string"}, "cases": {"elements": {"properties": {"name": {"type": "string"}}, "optionalProperties": {"next": {"type": "string"}, "conditional": {"ref": "conditional"}}}}}}}, "properties": {"name": {"type": "string"}, "workflow_json": {"properties": {"variables": {"values": {}}, "specVersion": {"type": "string"}, "start": {"type": "string"}, "states": {"elements": {"ref": "statement"}}}}}, "optionalProperties": {"id": {"type": "string"}, "designer_json": {}}}