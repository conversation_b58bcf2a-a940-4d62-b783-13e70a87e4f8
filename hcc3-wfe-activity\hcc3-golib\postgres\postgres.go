package aohpostgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang-migrate/migrate/v4"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
)

type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	Database string
	Schema   string
}

// NewConnString construct postgres connection string
func NewConnString(c Config) string {
	return fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable&search_path=%s",
		c.User, c.Password, c.Host, c.Port, c.Database, c.Schema)
}

// NewPostgresConn return db connection
func NewPostgresConn(connString string) (*pgx.Conn, error) {
	conn, err := pgx.Connect(context.Background(), connString)
	if err != nil {
		return nil, err
	}
	return conn, nil
}

// RunDbMigrate migrate sql up scripts from `migrationUrl` to db
func RunDbMigrate(migrationUrl string, connString string) error {
	m, err := migrate.New(migrationUrl, connString)
	if err != nil {
		return err
	}
	if err := m.Up(); err != nil && !errors.Is(err, migrate.ErrNoChange) {
		return err
	}
	return nil
}

// StringToUUID convert string into *uuid.UUID. return nil when string is empty
func StringToUUID(s string) *uuid.UUID {
	id, err := uuid.Parse(s)
	if err != nil {
		return nil
	}
	return &id
}

// UuidToString convert *uuid.UUID into string. return empty string when uuid is nil
func UuidToString(id *uuid.UUID) string {
	if id == nil || *id == uuid.Nil {
		return ""
	}
	return id.String()
}

// StringToTimestamptz convert string into pgtype.Timestamptz. return invalid pgtype.Timestamptz when string cannot be parsed
func StringToTimestamptz(s string) pgtype.Timestamptz {
	ts := pgtype.Timestamptz{Valid: true}
	if err := ts.Scan(s); err != nil {
		ts.Valid = false
	}
	return ts
}

// TimeToString convert time to string in RFC3339Nano format. return emtpy string if time is zero
func TimeToString(t time.Time) string {
	if !t.IsZero() {
		return t.Format(time.RFC3339Nano)
	}
	return ""
}
