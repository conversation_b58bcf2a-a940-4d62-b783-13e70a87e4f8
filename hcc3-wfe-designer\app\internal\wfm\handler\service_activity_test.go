package handler

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/mssfoobar/app/wfe/internal/model"
	mm "github.com/mssfoobar/app/wfe/internal/model/mock"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

var (
	testServiceActivityResp = model.ServiceActivityResponse{
		Id:              testId,
		ServiceName:     "testService",
		ActivityType:    "testActivityType",
		ActivityIcon:    "none",
		ActivityParam:   []byte(`[{"duration":"string"}]`),
		ActivityResult:  []byte(`{"object":{}}`),
		TimeoutInSecond: 300,
	}
)

func TestRegisterServiceActivity(t *testing.T) {
	factory := model.Factory{}
	handler := RegisterServiceActivity(&factory, "http://localhost:8080")

	req, err := http.NewRequest(http.MethodGet, "/service_activity", nil)
	if err != nil {
		t.Fatal(err)
	}

	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)

	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestServiceActivity_List(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockServiceActivityStore(ctrl)
	srvAct := ServiceActivity{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			ListServiceActivity(gomock.Any(), gomock.Any()).
			Return(&model.ListServiceActivityResponse{
				ServiceActivities: []model.ServiceActivityResponse{testServiceActivityResp},
				TotalCount:        1,
			}, nil)
		srvAct.List(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToSlice([]any{testServiceActivityResp}), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/?page=a&size=b", nil)
		r.Header.Add("Content-Type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		srvAct.List(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			ListServiceActivity(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("test error"))
		srvAct.List(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}
