import { env as envPrivate } from "$env/dynamic/private";
import {
	randomPKCECodeVerifier,
	calculatePKCECodeChallenge,
	type Configuration,
	buildAuthorizationUrl,
} from "openid-client";
import { StatusCodes } from "http-status-codes";
import { json } from "@sveltejs/kit";
import type { RequestHandler } from "./$types";
import { COOKIES_TYPE_ENUM, DEFAULT_COOKIE_OPTIONS } from "$lib/aoh/core/provider/auth/auth";

export const GET: RequestHandler = async (event) => {
	if (!event.locals.clients?.oidc_config) {
		throw new Error(
			"oidc_config missing from locals - required for authentication, please check your configuration for authentication"
		);
	}

	const oidc_config: Configuration = event.locals.clients.oidc_config;

	// Generate PKCE - Proof Key for Code Exchange
	const code_verifier = randomPKCECodeVerifier();
	const code_challenge = await calculatePKCECodeChallenge(code_verifier);

	let finalDestination: string;

	if (event.url.searchParams.has("url")) {
		finalDestination = envPrivate.ORIGIN + event.url.searchParams.get("url");
	} else {
		finalDestination = envPrivate.ORIGIN + ("/" + envPrivate.LOGIN_DESTINATION).replace("//", "/");
	}

	// Create OIDC Auth URL - where we will redirect the client to for authorization
	const redirectUrl: URL = buildAuthorizationUrl(oidc_config, {
		scope: "openid",
		resource: finalDestination,
		redirect_uri: finalDestination,
		code_challenge,
		code_challenge_method: "S256",
	});

	event.cookies.set(COOKIES_TYPE_ENUM.CODE_VERIFIER, code_verifier, DEFAULT_COOKIE_OPTIONS);
	event.setHeaders({
		Location: redirectUrl.href ?? "",
	});

	return json(null, {
		status: StatusCodes.TEMPORARY_REDIRECT,
	});
};
