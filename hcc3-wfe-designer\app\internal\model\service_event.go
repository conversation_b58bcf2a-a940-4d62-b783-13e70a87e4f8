package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

const (
	errDuplicateServiceEvent = "service event with name '%s' already exists"
	errServiceEventNotFound  = "service event id '%s' not found"
)

type sqlServiceEvent struct {
	Store
}

func newSqlServiceEvent(db sqlplugin.DB) *sqlServiceEvent {
	return &sqlServiceEvent{Store: NewStore(db)}
}

func (s sqlServiceEvent) CreateServiceEvent(
	ctx context.Context,
	request *CreateServiceEventRequest,
) (*ServiceEventResponse, error) {
	var resp *ServiceEventResponse
	err := s.txExecute(ctx, "CreateServiceEvent", func(tx sqlplugin.Tx) error {
		_, err := tx.InsertIntoServiceEvent(ctx, &sqlplugin.ServiceEventRow{
			Id:          uuid.New(),
			ServiceName: request.ServiceName,
			EventType:   request.EventType,
			EventIcon:   request.EventIcon,
			EventParam:  request.EventParam,
			EventResult: request.EventResult,
		})
		if err != nil {
			if s.Db.IsDupEntryError(err) {
				return NewStoreError(fmt.Sprintf(errDuplicateServiceEvent, request.ServiceName))
			}
			return err
		}
		return err
	})

	return resp, err
}

func (s sqlServiceEvent) UpdateServiceEvent(
	ctx context.Context,
	request *UpdateServiceEventRequest,
) (*ServiceEventResponse, error) {
	var resp *ServiceEventResponse
	err := s.txExecute(ctx, "UpdateServiceEvent", func(tx sqlplugin.Tx) error {
		result, err := s.Db.UpdateServiceEvent(ctx, &sqlplugin.ServiceEventRow{
			Id:          request.Id,
			ServiceName: request.ServiceName,
			EventType:   request.EventType,
			EventIcon:   request.EventIcon,
			EventParam:  request.EventParam,
			EventResult: request.EventResult,
		})
		if err != nil {
			return err
		}

		affectedRows, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if affectedRows == 0 {
			return NewStoreError(fmt.Sprintf(errServiceEventNotFound, request.Id))
		}

		resp, err = tx.SelectFromServiceEvent(ctx, sqlplugin.ServiceEventFilter{
			Id: request.Id,
		})
		return err
	})

	return resp, err
}

func (s sqlServiceEvent) GetServiceEvent(
	ctx context.Context,
	request *GetServiceEventRequest,
) (*ServiceEventResponse, error) {
	row, err := s.Db.SelectFromServiceEvent(ctx, sqlplugin.ServiceEventFilter{
		Id: request.Id,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, NewStoreError(fmt.Sprintf(errServiceEventNotFound, request.Id))
		}
		return nil, err
	}

	return row, nil
}

func (s sqlServiceEvent) ListServiceEvent(
	ctx context.Context,
	request *ListServiceEventRequest,
) (*ListServiceEventResponse, error) {
	var rows []sqlplugin.ServiceEventRow
	var total int
	var err error
	err = s.txExecute(ctx, "ListServiceEvent", func(tx sqlplugin.Tx) error {
		rows, err = s.Db.ListFromServiceEvent(ctx, sqlplugin.ServiceEventPaginateFilter{
			Limit:   request.Page.Size,
			Offset:  request.Page.Size * (request.Page.Number - 1),
			OrderBy: request.Page.Sorts.String(),
		})
		if err != nil {
			if s.Db.IsColumnNotExistError(err) {
				return NewStoreError("invalid query parameter; " + err.Error())
			}
			return err
		}

		total, err = s.Db.CountFromServiceEvent(ctx)

		return err
	})

	return &ListServiceEventResponse{
		TotalCount:    total,
		ServiceEvents: rows,
	}, err
}

func (s sqlServiceEvent) DeleteServiceEvent(
	ctx context.Context,
	request *DeleteServiceEventRequest,
) error {
	result, err := s.Db.DeleteFromServiceEvent(ctx, sqlplugin.ServiceEventFilter{
		Id: request.Id,
	})
	if err != nil {
		return err
	}

	affectedRows, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if affectedRows == 0 {
		return NewStoreError(fmt.Sprintf(errServiceEventNotFound, request.Id))
	}

	return nil
}
