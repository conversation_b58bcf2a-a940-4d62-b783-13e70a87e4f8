package activities

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mssfoobar/wfe_activity/internal/worker/constant"
	"mssfoobar/wfe_activity/internal/worker/dto"
	"mssfoobar/wfe_activity/internal/worker/util"
	"os"
	"strconv"
	"strings"
	"time"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/aoh-golib/temporal"
	"go.temporal.io/sdk/activity"
)

type Activities struct{}

type SysDatasourceResponse struct {
	IsTable         bool                     `json:"IsTable"`
	CalculatedValue string                   `json:"CalculatedValue"`
	MapValues       []map[string]interface{} `json:"MapValues"`
	Property        []string                 `json:"Property"`
}

type UpdateSysWFActivation struct {
	EndedAt        *time.Time       `json:"ended_at"`
	CurrentStatus  *string          `json:"current_status"`
	TargetDuration *int             `json:"target_duration"`
	IsActive       *bool            `json:"is_active"`
	Assignee       *string          `json:"assignee"`
	Metadata       *json.RawMessage `json:"metadata"`
}

type CreateSysWFActivationMilestoneDetail struct {
	DetailMsg string `json:"detail_msg"`
}

type CreateSysWFActivationMilestone struct {
	StartedAt      time.Time `json:"started_at"`
	TargetDuration int       `json:"target_duration"`
}

type UpdateSysWFActivationMilestone struct {
	EndedAt          *time.Time                              `json:"ended_at"`
	TargetDuration   *int                                    `json:"target_duration"`
	IsActive         *bool                                   `json:"is_active"`
	MilestoneDetails *[]CreateSysWFActivationMilestoneDetail `json:"milestone_details"`
}

type SysWFActivation struct {
	WfConfig  SysWFConfig `json:"wf_config"`
	IsActive  bool        `json:"is_active"`
	CreatedBy string      `json:"created_by"`
}
type SysWFConfig struct {
	Is_Closure_Rule_Met bool `gorm:"default:false"`
}

type EmailDTO struct {
	Recipient               []string `json:"recipient"`
	CCRecipient             []string `json:"cc_recipient"`
	BCCRecipient            []string `json:"bcc_recipient"`
	Subject                 string   `json:"subject"`
	HtmlBody                string   `json:"html_body"`
	Charset                 string   `json:"charset"`
	FilePaths               []string `json:"file_path"`
	C3UsersIdsRecipient     []string `json:"c3_users_ids_recipient"`
	C3RoleNamesRecipient    []string `json:"c3_role_names_recipient"`
	C3UsersIdsCCRecipient   []string `json:"c3_users_ids_cc_recipient"`
	C3RoleNamesCCRecipient  []string `json:"c3_role_names_cc_recipient"`
	C3UsersIdsBCCRecipient  []string `json:"c3_users_ids_bcc_recipient"`
	C3RoleNamesBCCRecipient []string `json:"c3_role_names_bcc_recipient"`
	TenantID                string   `json:"tenant_id"`
}

func getDataSourceValueByName(name string) (SysDatasourceResponse, error) {
	var sdr SysDatasourceResponse
	var DATA_AGG_URL = os.Getenv("DATA_AGG_URL")
	url := DATA_AGG_URL + "/final-data-aggregator/name/" + name
	respBody, err := util.ExecuteAPI(url, "GET", nil)
	if err != nil {
		return sdr, err
	}

	aohlog.Info("=====CALCULATED DATASOURCE========")
	aohlog.Info(string(respBody))

	err = json.Unmarshal(respBody, &sdr)
	if err != nil {
		return sdr, err
	}

	return sdr, nil
}

func (a *Activities) GetDataNumber(ctx context.Context, dataSourceName string) (int, error) {
	if dataSourceName != "" {
		valueRes, err := getDataSourceValueByName(dataSourceName)
		if err != nil {
			fmt.Println(err.Error())
			return 0, err
		}
		intValue, err := strconv.Atoi(valueRes.CalculatedValue)
		if err != nil {
			return 0, err
		}
		return intValue, nil
	}

	return 0, nil
}

func (a *Activities) GetDataString(ctx context.Context, dataSourceName string) (string, error) {
	if dataSourceName != "" {
		valueRes, err := getDataSourceValueByName(dataSourceName)
		if err != nil {
			fmt.Println(err.Error())
			return "", err
		}
		return valueRes.CalculatedValue, nil
	}

	return "", nil
}

func (a *Activities) wait(ctx context.Context, totalSeconds int, heartbeatMessage string, workflowID string) bool {
	ticker := time.NewTicker(10 * time.Second)
	timer := time.NewTimer(time.Duration(totalSeconds) * time.Second)
	checkActivationTimer := time.NewTicker(1 * time.Minute)
	defer timer.Stop()
	defer ticker.Stop()
	defer checkActivationTimer.Stop()

	aohlog.Info("Waiting for: " + strconv.Itoa(totalSeconds) + " seconds for " + heartbeatMessage)

	for {
		select {
		case <-ticker.C:
			// Periodic heartbeat
			if ctx.Err() != nil {
				aohlog.Info("Context was canceled during heartbeat: " + ctx.Err().Error())
				return false
			}
			activity.RecordHeartbeat(ctx, heartbeatMessage)

		case <-checkActivationTimer.C:
			wfActivation, err := a.getWFActivation(workflowID)
			if err != nil {
				aohlog.Info("Error checking workflow activation: " + err.Error())
				return false
			}
			if !wfActivation.IsActive {
				aohlog.Info("Workflow is finished, stopping wait...")
				return false
			}

		case <-timer.C:
			aohlog.Info("Completed wait for " + heartbeatMessage)
			return true

		case <-ctx.Done():
			aohlog.Info("Context canceled during wait: " + ctx.Err().Error())
			wfActivation, err := a.getWFActivation(workflowID)
			if err != nil {
				aohlog.Info("Error checking workflow activation: " + err.Error())
				return false
			}
			if wfActivation.IsActive {
				aohlog.Info("Workflow is finished without DeactivatedSOP. Trying to deactivate SOP...")
				updateWorkflowActivationAndMilestone(workflowID, nil, false, nil, nil)
			}
			return false
		}
	}
}

func (a *Activities) WaitInterval(ctx context.Context, days int, hours int, minutes int, seconds int) (int, error) {
	// Calculate total seconds from input (days, hours, minutes, seconds)
	totalSeconds := (days * 24 * 60 * 60) + (hours * 60 * 60) + (minutes * 60) + seconds
	workflowID, err := getWorkflowID(ctx)
	if err != nil {
		aohlog.Info("Error getting workflow ID: " + err.Error())
		return 0, err
	}

	if isCompletedWaiting := a.wait(ctx, int(totalSeconds), "Heartbeat from WaitInterval activity", workflowID); !isCompletedWaiting {
		// If the wait was interrupted (context canceled or completed), log it
		aohlog.Info("Context for WaitInterval activity was canceled")
		return 0, errors.New("context for WaitInterval activity was canceled")
	}

	// If the wait was completed successfully, return the totalSeconds
	return totalSeconds, nil
}

func (a *Activities) SendSOPEmail(ctx context.Context, subject string,
	messageBody string,
	recipientsEmail string,
	recipientsUsers string,
	recipientsRoles string,
	ccEmail string,
	ccUsers string,
	ccRoles string,
	bccEmail string,
	bccUsers string,
	bccRoles string) ([]byte, error) {
	workflowID, err := getWorkflowID(ctx)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}

	wfActivation, err := a.getWFActivation(workflowID)
	if err != nil {
		return nil, err
	}

	if !wfActivation.IsActive {
		aohlog.Warn("Workflow is not active, cannot send email")
		return nil, errors.New("workflow is not active")
	}

	//Calculate dynamic variables
	MessageBody := messageBody
	variablesMap := make(map[string]string)

	// Collect variables from MessageBody
	// Assuming variables are enclosed in double curly braces {{}}
	reader := bufio.NewReader(strings.NewReader(MessageBody))
	for {
		char, _, err := reader.ReadRune()
		if err == io.EOF {
			break
		}
		if char == '{' {
			// Check if the next character is also '{'
			if nextChar, err := reader.Peek(1); err == nil && string(nextChar) == "{" {
				// Read the variable name
				variableName, err := readVariableName(reader)
				if err != nil {
					fmt.Println(err.Error())
					continue
				}

				// Add the variable to the map
				variablesMap[variableName] = ""
			}
		}
	}

	// Loop through the variablesMap and call getDataSourceValueByName for each key
	for key := range variablesMap {
		if key == constant.UsernameKey {
			variablesMap[key] = wfActivation.CreatedBy
			continue
		}

		valueRes, err := getDataSourceValueByName(key)
		if err != nil {
			fmt.Println(err.Error())
			continue
		}
		if valueRes.CalculatedValue != "" {
			variablesMap[key] = valueRes.CalculatedValue
		} else if len(valueRes.MapValues) > 0 {
			if valueRes.IsTable {
				var tableBuffer bytes.Buffer
				orderedHeader := valueRes.Property
				tableBuffer.WriteString("<table>")
				tableBuffer.WriteString("<tr>")
				for _, k := range orderedHeader {
					tableBuffer.WriteString("<th>" + util.ConvertToTitleCase(k) + "</th>")
				}
				tableBuffer.WriteString("</tr>")

				for _, m := range valueRes.MapValues {
					tableBuffer.WriteString("<tr>")
					for _, k := range orderedHeader {
						tableBuffer.WriteString("<td>" + fmt.Sprint(m[k]) + "</td>")
					}
					tableBuffer.WriteString("</tr>")
				}
				tableBuffer.WriteString("</table>")
				variablesMap[key] = tableBuffer.String()
			} else {
				for _, v := range valueRes.MapValues[0] {
					variablesMap[key] = fmt.Sprint(v)
					break
				}
			}
		}

	}

	// Replace variables in MessageBody with values from variablesMap
	for key, value := range variablesMap {
		MessageBody = strings.ReplaceAll(MessageBody, "{{"+key+"}}", value)
	}
	fmt.Println(MessageBody)

	// Prepare data from sending email
	recipientUsernames, recipientEmails := util.GetUsernamesAndEmails(recipientsEmail)
	_, ccRecipientEmails := util.GetUsernamesAndEmails(ccEmail)
	_, bccRecipientEmails := util.GetUsernamesAndEmails(bccEmail)

	c3RecipientUsernames, c3UsersIdsRecipient := util.GetUsernamesAndIds(recipientsUsers)
	_, c3UsersIdsCcRecipient := util.GetUsernamesAndIds(ccUsers)
	_, c3UsersIdsBccRecipient := util.GetUsernamesAndIds(bccUsers)

	//Call API sendEmail
	var COMMON_URL = os.Getenv("COMMON_URL")
	tenantID, err := getTenantIDFromContext(ctx)
	if err != nil {
		aohlog.Errorf("Failed to retrieve tenant ID from context: %s", err.Error())
		tenantID = "1f4e431a-2c95-4488-92d4-f8d94ed96322"
	} else if tenantID == "" {
		aohlog.Warn("Tenant ID is empty, using fallback value")
		tenantID = "1f4e431a-2c95-4488-92d4-f8d94ed96322"
	}

	emailDTO := EmailDTO{
		Recipient:               recipientEmails,
		CCRecipient:             ccRecipientEmails,
		BCCRecipient:            bccRecipientEmails,
		Subject:                 subject,
		HtmlBody:                MessageBody,
		Charset:                 "UTF-8",
		FilePaths:               []string{},
		C3UsersIdsRecipient:     c3UsersIdsRecipient,
		C3RoleNamesRecipient:    util.TrimStringSlice(strings.Split(strings.TrimSpace(recipientsRoles), ",")),
		C3UsersIdsCCRecipient:   c3UsersIdsCcRecipient,
		C3RoleNamesCCRecipient:  util.TrimStringSlice(strings.Split(strings.TrimSpace(ccRoles), ",")),
		C3UsersIdsBCCRecipient:  c3UsersIdsBccRecipient,
		C3RoleNamesBCCRecipient: util.TrimStringSlice(strings.Split(strings.TrimSpace(bccRoles), ",")),
		TenantID:                tenantID,
	}

	// Convert emailDTO to JSON
	emailJSON, err := json.Marshal(emailDTO)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}

	updateWorkflowActivationAndMilestone(workflowID, &MessageBody, true, nil, nil)

	// Call API sendEmail /aws-ses
	sendEmailUrl := COMMON_URL + "/aws-ses"
	token, err := util.GetAccessTokenFromCommon()
	if err != nil {
		aohlog.Error("Failed to get access token: " + err.Error())
		return nil, err
	}
	_, err = util.ExecuteAPIWithAuth(sendEmailUrl, "POST", bytes.NewBuffer(emailJSON), token)
	if err != nil {
		aohlog.Error("Executing SendSOPEmail error: " + err.Error())
	}

	err = util.UpdateAssignee(workflowID, recipientUsernames, nil, nil, c3RecipientUsernames, nil, nil)
	if err != nil {
		aohlog.Error("Executing SendSOPEmail error: " + err.Error())
	}

	return []byte(MessageBody), nil
}

func (a *Activities) SetDefaultParameter(ctx context.Context, targetDuration int, amberThreshold int, redThreshold int) ([]byte, error) {
	workflowID, err := getWorkflowID(ctx)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}

	//Handling usecase sys_wf_activation is not create on time
	for i := 0; i < 3; i++ {
		err := createMilestone(workflowID, targetDuration)
		if err == nil {
			break
		}
		if i == 2 {
			return nil, nil
		}
		time.Sleep(3 * time.Second) // Wait for 3 seconds before retrying
	}

	// Create metadata for activation
	jsonValue, err := json.Marshal(map[string]interface{}{
		"amber_threshold": amberThreshold,
		"red_threshold":   redThreshold,
	})
	if err != nil {
		return nil, err
	}
	metadata := json.RawMessage(jsonValue)

	err = updateSysWFActivation(workflowID, nil, true, nil, &targetDuration, &metadata)
	if err != nil {
		return nil, err
	}
	return []byte(""), nil
}

func (a *Activities) CheckClosureRules(ctx context.Context, input []byte) (bool, error) {
	workflowID, err := getWorkflowID(ctx)
	if err != nil {
		aohlog.Info("Error getting workflow ID: " + err.Error())
		return false, err
	}

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	timer := time.NewTicker(1 * time.Minute)
	defer timer.Stop()
	aohlog.Info("Started CheckClosureRules activity")

	for {
		select {
		case <-timer.C:
			wfActivation, err := a.getWFActivation(workflowID)
			if err != nil {
				return false, err
			}
			if wfActivation.WfConfig.Is_Closure_Rule_Met {
				aohlog.Info("Closure rule met, exiting...")
				return true, nil
			}
			aohlog.Info("Closure rule not met, retrying...")

		case <-ticker.C:
			if ctx.Err() != nil {
				aohlog.Info("Context was canceled during heartbeat: " + ctx.Err().Error())
				return false, nil
			}
			activity.RecordHeartbeat(ctx, "Heartbeat from CheckClosureRules activity")

		case <-ctx.Done():
			aohlog.Info("Context canceled, stopping CheckClosureRules activity")
			wfActivation, err := a.getWFActivation(workflowID)
			if err != nil {
				aohlog.Info("Error checking workflow activation: " + err.Error())
				return false, nil
			}
			if wfActivation.IsActive {
				aohlog.Info("Workflow is finished without DeactivatedSOP. Trying to deactivate SOP...")
				updateWorkflowActivationAndMilestone(workflowID, nil, false, nil, nil)
			}
			return false, nil
		}
	}
}

func (a *Activities) DeactivateSOP(ctx context.Context, input []byte) error {
	workflowID, err := getWorkflowID(ctx)
	if err != nil {
		fmt.Println(err.Error())
		return err
	}

	updateWorkflowActivationAndMilestone(workflowID, nil, false, nil, nil)

	return nil
}

func (a *Activities) SetMilestone(ctx context.Context, milestoneNumber int, targetDuration int, milestoneActions string) error {
	workflowID, err := getWorkflowID(ctx)
	if err != nil {
		aohlog.Error(err.Error())
		return err
	}

	var SOP_URL = os.Getenv("SOP_URL")
	createOrUpdateMilestoneUrl := SOP_URL + "/sys-wf-activation/" + workflowID + "/milestone"

	// Generate metadata
	actionsList := []string{}
	for _, action := range strings.Split(milestoneActions, ",") {
		trimmedAction := strings.TrimSpace(action)
		if trimmedAction != "" {
			actionsList = append(actionsList, trimmedAction)
		}
	}

	metadataMap := map[string]interface{}{
		"actions": actionsList,
	}
	metadataJSON, err := json.Marshal(metadataMap)
	if err != nil {
		return err
	}
	metadata := json.RawMessage(metadataJSON)

	createOrUpdateMilestoneRequest := dto.CreateOrUpdateSysWFActivationMilestone{
		MilestoneNumber: &milestoneNumber,
		TargetDuration:  targetDuration,
		Metadata:        &metadata,
	}
	requestJSON, err := json.Marshal(createOrUpdateMilestoneRequest)
	if err != nil {
		return err
	}

	_, err = util.ExecuteAPI(createOrUpdateMilestoneUrl, "PUT", bytes.NewBuffer(requestJSON))
	if err != nil {
		return err
	}

	return nil
}

func (a *Activities) GetUserResponse(ctx context.Context, milestoneNumber int) (string, error) {
	ticker := time.NewTicker(3 * time.Second)

	defer ticker.Stop()
	attempts := 0
	maxAttempts := 99999

	sopUrl := os.Getenv("SOP_URL")

	workflowID, err := getWorkflowID(ctx)
	if err != nil {
		aohlog.Error(err.Error())
		return "", err
	}

	milestoneDetailUrl := fmt.Sprintf("%s/sys-wf-activation/%s/milestone/%d", sopUrl, workflowID, milestoneNumber)
	updateMilestoneUrl := sopUrl + "/sys-wf-activation/" + workflowID + "/milestone"
	for {
		select {
		case <-ticker.C:
			// Update milestone duration
			isActive := true
			updateMilestone := UpdateSysWFActivationMilestone{
				IsActive: &isActive,
			}
			updateMilestoneJSON, err := json.Marshal(updateMilestone)
			if err != nil {
				aohlog.Errorf("Fail to extract the json for update milestone: %s", err.Error())
			}
			_, err = util.ExecuteAPI(updateMilestoneUrl, "PATCH", bytes.NewBuffer(updateMilestoneJSON))
			if err != nil {
				aohlog.Errorf("Fail to update the milestone: %s", err.Error())
			}

			milestoneDetail, err := util.ExecuteAPI(milestoneDetailUrl, "GET", nil)
			if err == nil {
				var milestoneDetailResponse dto.SysWFActivationMilestoneResponse
				if err := json.Unmarshal(milestoneDetail, &milestoneDetailResponse); err == nil {
					nextAction := util.GetMilestoneAction(&milestoneDetailResponse)
					if nextAction != "" {
						return nextAction, nil
					}
				}
			}

			activity.RecordHeartbeat(ctx, "Waiting for user response")
			aohlog.Info("Waiting for user response")
			attempts++

			// Default -> return first action
			if attempts >= maxAttempts {
				// Consider to terminate the workflow
				aohlog.Info("Cannot get user response, use the first action")
				return "", fmt.Errorf("maximum attempts reached waiting for user response")
			}
		case <-ctx.Done():
			return "", ctx.Err()
		}
	}
}

func (a *Activities) GetTriggerType(ctx context.Context) (*bool, error) {
	workflowID, err := getWorkflowID(ctx)
	if err != nil {
		aohlog.Error(err.Error())
		return nil, err
	}

	activationDetailUrl := fmt.Sprintf("%s/sys-wf-activation/%s", os.Getenv("SOP_URL"), workflowID)

	activationDetail, err := util.ExecuteAPI(activationDetailUrl, "GET", nil)
	if err != nil {
		return nil, err
	}

	var activationDetailResponse dto.SysWFActivationResponse
	if err := json.Unmarshal(activationDetail, &activationDetailResponse); err == nil {
		return &activationDetailResponse.IsManualTrigger, nil
	}
	return nil, err
}

func (a *Activities) getWFActivation(workflowID string) (*SysWFActivation, error) {
	var SOP_URL = os.Getenv("SOP_URL")
	getWorkflowActivation := SOP_URL + "/sys-wf-activation/" + workflowID

	respActivation, err := util.ExecuteAPI(getWorkflowActivation, "GET", nil)
	if err != nil {
		aohlog.Info("Error executing API: " + err.Error())
		return nil, err
	}

	var wfActivation SysWFActivation
	err = json.Unmarshal(respActivation, &wfActivation)
	if err != nil {
		aohlog.Info("Error unmarshalling API response: " + err.Error())
		return nil, err
	}

	return &wfActivation, nil
}

func createMilestone(workflowID string, targetDuration int) error {
	// Create sys_wf_activation_milestone table
	var SOP_URL = os.Getenv("SOP_URL")
	createMilestoneUrl := SOP_URL + "/sys-wf-activation/" + workflowID + "/milestone"
	createMilestone := CreateSysWFActivationMilestone{
		TargetDuration: targetDuration,
	}
	createMilestoneJSON, err := json.Marshal(createMilestone)
	if err != nil {
		return err
	}
	_, err = util.ExecuteAPI(createMilestoneUrl, "POST", bytes.NewBuffer(createMilestoneJSON))
	if err != nil {
		fmt.Println(err.Error())
		return err
	}

	return nil
}

func getWorkflowID(ctx context.Context) (string, error) {
	workflowInfo := activity.GetInfo(ctx)
	workflowID := workflowInfo.WorkflowExecution.ID
	return workflowID, nil
}

func updateSysWFActivation(workflowID string, MessageBody *string, isActive bool, Assignee *string, targetDuration *int, metadata *json.RawMessage) error {
	var SOP_URL = os.Getenv("SOP_URL")

	// Update sys_wf_activation table
	updateActivationUrl := SOP_URL + "/sys-wf-activation/" + workflowID
	updateActivation := UpdateSysWFActivation{
		Assignee:       Assignee,
		CurrentStatus:  MessageBody,
		IsActive:       &isActive,
		TargetDuration: targetDuration,
		Metadata:       metadata,
	}
	updateActivationJSON, err := json.Marshal(updateActivation)
	if err != nil {
		return err
	}
	_, err = util.ExecuteAPI(updateActivationUrl, "PATCH", bytes.NewBuffer(updateActivationJSON))
	if err != nil {
		fmt.Println(err.Error())
		return err
	}

	return nil
}

func updateWorkflowActivationAndMilestone(workflowID string, MessageBody *string, isActive bool, Assignee *string, targetDuration *int) error {
	var SOP_URL = os.Getenv("SOP_URL")
	updateSysWFActivation(workflowID, MessageBody, isActive, Assignee, targetDuration, nil)

	// Update sys_wf_activation_milestone & sys_wf_activation_milestone_detail table
	updateMilestoneUrl := SOP_URL + "/sys-wf-activation/" + workflowID + "/milestone"
	updateMilestone := UpdateSysWFActivationMilestone{
		IsActive: &isActive,
	}
	if MessageBody != nil {
		updateMilestone.MilestoneDetails = &[]CreateSysWFActivationMilestoneDetail{
			{
				DetailMsg: *MessageBody,
			},
		}
	}

	updateMilestoneJSON, err := json.Marshal(updateMilestone)
	if err != nil {
		return err
	}
	_, err = util.ExecuteAPI(updateMilestoneUrl, "PATCH", bytes.NewBuffer(updateMilestoneJSON))
	if err != nil {
		fmt.Println(err.Error())
		return err
	}

	return nil
}

func readVariableName(reader *bufio.Reader) (string, error) {
	var variableName strings.Builder
	for {
		char, _, err := reader.ReadRune()
		if err != nil {
			fmt.Println(err.Error())
			return "", err
		}
		if char == '}' {
			// Check if the next character is also '}'
			if nextChar, err := reader.Peek(1); err == nil && string(nextChar) == "}" {
				// Consume the next character
				reader.ReadRune()
				break
			}
		}
		variableName.WriteRune(char)
	}
	return variableName.String()[1:], nil
}

func getTenantIDFromContext(ctx context.Context) (string, error) {
	metadata := temporal.ContextValue(ctx)
	if metadata != nil {
		if tenantIDValue, exists := metadata["tenant_id"]; exists {
			if tenantIDStr, valid := tenantIDValue.(string); valid && tenantIDStr != "" {
				return tenantIDStr, nil
			}
		}
	}
	return "", fmt.Errorf("tenant_id not found in context metadata")
}
