// Package worker register activities and start temporal worker
package worker

import (
	"context"
	"errors"
	"net"
	"net/http"
	"sync"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/aoh-golib/temporal"
	"mssfoobar/wfe_activity/internal/config"
	"mssfoobar/wfe_activity/internal/worker/activities"
	"mssfoobar/wfe_activity/internal/worker/handler"

	"go.temporal.io/sdk/worker"
	"go.uber.org/zap"
)

type Worker struct {
	http *http.Server
	wg   sync.WaitGroup
}

func New() *Worker {
	return &Worker{}
}

// runWorker initialize temporal worker instance
func (w *Worker) runWorker(c config.Option, interruptCh <-chan interface{}) error {
	client, err := temporal.NewClient(temporal.Config{
		HostPort:  c.Temporal.Host + ":" + c.Temporal.Port,
		Namespace: c.Temporal.Namespace,
	})
	if err != nil {
		return err
	}
	defer client.Close()

	wk := worker.New(client, c.<PERSON>, worker.Options{
		DisableWorkflowWorker: true,
	})

	wk.RegisterActivity(&activities.Activities{})

	return wk.Run(interruptCh)
}

func (w *Worker) Start(c config.Option) {
	interruptCh := make(chan interface{})
	w.http = &http.Server{
		Addr:    net.JoinHostPort("0.0.0.0", c.Port),
		Handler: handler.NewHealthRouter(),
	}

	aohlog.Info("[INIT] http server starting", zap.String("port", c.Port))
	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		if err := w.http.ListenAndServe(); !errors.Is(err, http.ErrServerClosed) {
			aohlog.Fatal("unable to start http server", zap.Error(err))
		}
		aohlog.Info("[SHUTDOWN] http server shutdown")
		interruptCh <- struct{}{}
	}()

	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		if err := w.runWorker(c, interruptCh); err != nil {
			aohlog.Fatal("unable to start temporal worker", zap.Error(err))
		}
		aohlog.Info("[SHUTDOWN] temporal worker shutdown")
	}()
}

// Stop gracefully exit the app
func (w *Worker) Stop() {
	aohlog.Info("[SHUTDOWN] workflow worker shutting down")
	err := w.http.Shutdown(context.Background())
	if err != nil {
		aohlog.Error("[SHUTDOWN] http server shutdown error", zap.Error(err))
	}
	w.wg.Wait()
	aohlog.Info("[SHUTDOWN] workflow worker shutdown successfully")
}
