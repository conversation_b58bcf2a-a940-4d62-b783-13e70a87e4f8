package handler

import (
	"errors"
	"net/http"

	"github.com/expr-lang/expr"
	exprFile "github.com/expr-lang/expr/file"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"go.uber.org/zap"
)

type Validate struct{}

type validateReqBody struct {
	Expression string                 `json:"expression"`
	Variables  map[string]interface{} `json:"variables"`
}

func (v *validateReqBody) Bind(_ *http.Request) error {
	if v.Expression == "" {
		return errors.New("expression is required")
	}
	return nil
}

func RegisterValidate(keycloakUrl string) http.Handler {
	r := chi.NewRouter()
	util.SetMiddleware(r, keycloakUrl)

	v := Validate{}

	r.Route("/", func(r chi.Router) {
		r.Post("/expression", v.Expression)
		r.Post("/condition", v.Condition)
	})

	return r
}

// Expression checks whether the expression in request body is valid or not
func (v *Validate) Expression(w http.ResponseWriter, r *http.Request) {
	req := validateReqBody{}
	if err := render.Bind(r, &req); err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
		return
	}

	_, err := expr.Compile(req.Expression, expr.Env(req.Variables))
	if err != nil {
		aohlog.Error("[Validate] compile expression failed",
			zap.String("expression", req.Expression),
			zap.Any("variables", req.Variables),
			zap.Error(err))
		var exprErr *exprFile.Error
		if errors.As(err, &exprErr) {
			resErr := errors.New(exprErr.Message)
			resData := []any{exprErr}
			resRender := aohhttp.ErrResponseWithData(http.StatusBadRequest, "", resData, []error{resErr})
			_ = render.Render(w, r, resRender)
			return
		}
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "invalid expression", []error{err}))
		return
	}

	aohlog.Info("[Validate] expression is valid", zap.String("expression", req.Expression))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "valid expression", nil))
}

// Condition checks whether the expression in the payload is resolved to true/false
func (v *Validate) Condition(w http.ResponseWriter, r *http.Request) {
	req := validateReqBody{}
	if err := render.Bind(r, &req); err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
		return
	}

	program, err := expr.Compile(
		req.Expression,
		expr.AsBool(),
		expr.Env(req.Variables),
	)
	if err != nil {
		aohlog.Error("[Validate] compile condition failed",
			zap.String("expression", req.Expression),
			zap.Any("variables", req.Variables),
			zap.Error(err))
		var exprErr *exprFile.Error
		if errors.As(err, &exprErr) {
			resErr := errors.New(exprErr.Message)
			resData := []any{exprErr}
			resRender := aohhttp.ErrResponseWithData(http.StatusBadRequest, "", resData, []error{resErr})
			_ = render.Render(w, r, resRender)
			return
		}
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "invalid condition", []error{err}))
		return
	}

	_, err = expr.Run(program, req.Variables)
	if err != nil {
		aohlog.Error("[Validate] run condition failed",
			zap.String("expression", req.Expression),
			zap.Any("variables", req.Variables),
			zap.Error(err))
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "invalid condition", []error{err}))
		return
	}

	aohlog.Info("[Validate] condition is valid", zap.String("expression", req.Expression))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "valid condition", nil))
}
