package aohhttp

import (
	"net/http"
	"sync"

	"github.com/go-chi/chi/v5"
)

type Check func() error

type HealthCheck interface {
	AddLivenessCheck(name string, check Check)
	AddReadinessCheck(name string, check Check)
	LiveEndpoint(w http.ResponseWriter, r *http.Request)
	ReadyEndpoint(w http.ResponseWriter, r *http.Request)
}

// handler is a basic Handler implementation.
type handler struct {
	http.ServeMux
	checksMutex     sync.RWMutex
	livenessChecks  map[string]Check
	readinessChecks map[string]Check
}

// NewHealthCheck create liveness and readiness probe check http handler for kubernetes.
// Their endpoints are at "/livez" and "/readyz" respectively.
//
// To add healthcheck handlers, use [HealthCheck.AddLivenessCheck] & [HealthCheck.AddReadinessCheck]
//
// Example:
//
//	 health := NewHealthCheck(router)
//	 health.AddReadinessCheck("readiness check", func() error {
//	 	// do readiness checking here
//			return nil
//	 }
func NewHealthCheck(router *chi.Mux) HealthCheck {
	h := &handler{
		livenessChecks:  make(map[string]Check),
		readinessChecks: make(map[string]Check),
	}
	router.Get("/livez", h.LiveEndpoint)
	router.Get("/readyz", h.ReadyEndpoint)
	return h
}

func (s *handler) LiveEndpoint(w http.ResponseWriter, r *http.Request) {
	s.handle(w, r, s.livenessChecks)
}

func (s *handler) ReadyEndpoint(w http.ResponseWriter, r *http.Request) {
	s.handle(w, r, s.readinessChecks)
}

func (s *handler) AddLivenessCheck(name string, check Check) {
	s.checksMutex.Lock()
	defer s.checksMutex.Unlock()
	s.livenessChecks[name] = check
}

func (s *handler) AddReadinessCheck(name string, check Check) {
	s.checksMutex.Lock()
	defer s.checksMutex.Unlock()
	s.readinessChecks[name] = check
}

func (s *handler) collectChecks(checks map[string]Check, resultsOut map[string]string, statusOut *int) {
	s.checksMutex.RLock()
	defer s.checksMutex.RUnlock()
	for name, check := range checks {
		if err := check(); err != nil {
			*statusOut = http.StatusServiceUnavailable
			resultsOut[name] = err.Error()
		} else {
			resultsOut[name] = "OK"
		}
	}
}

func (s *handler) handle(w http.ResponseWriter, r *http.Request, checks ...map[string]Check) {
	checkResults := make(map[string]string)
	status := http.StatusOK
	for _, checks := range checks {
		s.collectChecks(checks, checkResults, &status)
	}
	w.WriteHeader(status)
}
