<!-- More on how to set up stories at: https://storybook.js.org/docs/writing-stories -->
<!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->

<script module lang="ts">
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import Headerbar from "./index.svelte";

	const { Story } = defineMeta({
		title: "AOH/Core/Headerbar",
		component: Headerbar,
		tags: ["autodocs"],
		argTypes: {
			title: {
				control: "text",
				description: "The title of the page to show in the headerbar",
			},
		},
		args: {
			title: "My Page Title!",
		},
	});
</script>

<Story name="Primary" />
