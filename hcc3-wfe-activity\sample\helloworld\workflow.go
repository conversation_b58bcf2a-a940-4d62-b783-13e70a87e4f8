package helloworld

import (
	"time"

	"go.temporal.io/sdk/workflow"
)

// Workflow is a workflow definition.
func Workflow(ctx workflow.Context) (string, error) {
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: 10 * time.Second,
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	logger := workflow.GetLogger(ctx)
	logger.Info("Workflow started")

	var result string
	var activities Activities
	// here call your activity with arguments
	err := workflow.ExecuteActivity(ctx, activities.HelloWorld, "Hi!").Get(ctx, &result)
	if err != nil {
		logger.Error("Activity failed.", "Error", err)
		return "", err
	}

	logger.Info("Workflow completed.", "result", result)

	return result, nil
}
