package postgresql

import (
	"context"
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
	"github.com/mssfoobar/app/wfe/schema/postgres"
)

const ErrForeignKeyViolationCode = pq.ErrorCode("23503")
const ErrDupEntryCode = pq.ErrorCode("23505")
const ErrColumnNotExistCode = pq.ErrorCode("42703")
const ErrExceptionCode = pq.ErrorCode("P0001")

// db represents a logical connection to mysql database
type db struct {
	dbName     string
	schemaName string

	db   *sqlx.DB
	tx   *sqlx.Tx
	conn sqlplugin.Conn
}

func (pdb *db) IsForeignKeyViolationError(err error) bool {
	var sqlErr *pq.Error
	ok := errors.As(err, &sqlErr)
	return ok && sqlErr.Code == ErrForeignKeyViolationCode
}

func (pdb *db) IsDupEntryError(err error) bool {
	var sqlErr *pq.Error
	ok := errors.As(err, &sqlErr)
	return ok && sqlErr.Code == ErrDupEntryCode
}

func (pdb *db) IsColumnNotExistError(err error) bool {
	var sqlErr *pq.Error
	ok := errors.As(err, &sqlErr)
	return ok && sqlErr.Code == ErrColumnNotExistCode
}

// IsExceptionError checks for error when there is occ lock value mismatch during update
func (pdb *db) IsExceptionError(err error) bool {
	var sqlErr *pq.Error
	ok := errors.As(err, &sqlErr)
	return ok && sqlErr.Code == ErrExceptionCode
}

var _ sqlplugin.DB = (*db)(nil) // check interface implementation
var _ sqlplugin.Tx = (*db)(nil) // check interface implementation

// newDB returns an instance of DB, which is a logical
// connection to the underlying postgresql database
func newDB(
	dbName string,
	schemaName string,
	xdb *sqlx.DB,
	tx *sqlx.Tx,
) *db {
	mdb := &db{
		dbName:     dbName,
		schemaName: schemaName,
		db:         xdb,
		tx:         tx,
	}
	mdb.conn = xdb
	if tx != nil {
		mdb.conn = tx
	}
	return mdb
}

// BeginTx starts a new transaction and returns a reference to the Tx object
func (pdb *db) BeginTx(ctx context.Context) (sqlplugin.Tx, error) {
	xtx, err := pdb.db.BeginTxx(ctx, nil)
	if err != nil {
		return nil, err
	}
	return newDB(pdb.dbName, pdb.schemaName, pdb.db, xtx), nil
}

// Commit commits a previously started transaction
func (pdb *db) Commit() error {
	return pdb.tx.Commit()
}

// Rollback triggers rollback of a previously started transaction
func (pdb *db) Rollback() error {
	return pdb.tx.Rollback()
}

// Close closes the connection to the mysql db
func (pdb *db) Close() error {
	return pdb.db.Close()
}

// PluginName returns the name of the mysql plugin
func (pdb *db) PluginName() string {
	return PluginName
}

// DbName returns the name of the database
func (pdb *db) DbName() string {
	return pdb.dbName
}

func (pdb *db) SchemaName() string {
	return pdb.schemaName
}

func (pdb *db) ExpectedVersion() string {
	return postgres.Version
}

func (pdb *db) VerifyVersion() error {
	v, err := pdb.ReadSchemaVersion()
	if err != nil {
		return err
	}
	if postgres.Version != v {
		return fmt.Errorf("db schema version mismatched; expected '%s' found '%s'", v, postgres.Version)
	}
	return nil
}
