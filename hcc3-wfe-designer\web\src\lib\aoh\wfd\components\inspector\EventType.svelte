<script lang="ts">
	import type { ui } from "rappid/rappid";
	import * as Select from "$lib/aoh/wfd/components/ui/select";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { Event } from "$lib/aoh/wfd/bpmn/shapes";

	let { inspector, path }: { inspector: ui.Inspector; path: string } = $props();

	const cell = inspector.options.cell as Event;
	const options = [
		{
			value: "solid",
			content: "Yes",
		},
		{
			value: "dashed",
			content: "No",
		},
	];

	let selected = $state(cell.prop(path) === "dashed" ? "No" : "Yes");

	const applyChangeToCell = () => {
		cell.prop(path, selected);
	};

	const updateCellValue = () => {
		// shad-cn popover closing animation is slower than jointJS inspector refresh
		// so we need to delay the applyChangeToCell function
		setTimeout(() => {
			applyChangeToCell();
		}, 200);
	};
	const triggerContent = $derived(options.find((f) => f.content === selected)?.content ?? options[0].content);
</script>

<Label>Interrupting</Label>
<Select.Root type="single" allowDeselect={false} bind:value={selected} onValueChange={updateCellValue}>
	<Select.Trigger class="w-full">
		{triggerContent}
	</Select.Trigger>
	<Select.Content>
		<Select.Group>
			{#each options as opt}
				<Select.Item value={opt.value} label={opt.content}>{opt.content}</Select.Item>
			{/each}
		</Select.Group>
	</Select.Content>
</Select.Root>
