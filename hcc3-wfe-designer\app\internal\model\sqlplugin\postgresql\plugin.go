package postgresql

import (
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"
	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/mssfoobar/app/wfe/internal/model"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin/postgresql/session"
)

const PluginName = "postgres"

type plugin struct{}

var _ sqlplugin.Plugin = (*plugin)(nil) // check interface implementation

func init() {
	model.RegisterPlugin(PluginName, &plugin{})
}

// CreateDB initialize the db object
func (d *plugin) CreateDB(cfg *config.SQL) (sqlplugin.DB, error) {
	return d.createDB(cfg)
}

func (d *plugin) CreateModuleInfoDB(cfg *config.SQL) (sqlplugin.ModuleInfoDB, error) {
	return d.createDB(cfg)
}

func (d *plugin) createDB(cfg *config.SQL) (*db, error) {
	conn, err := d.createDBConnection(cfg)
	if err != nil {
		return nil, err
	}
	db := newDB(cfg.DatabaseName, cfg.SchemaName, conn, nil)
	return db, nil
}

// CreateDBConnection creates a returns a reference to a logical connection to the
// underlying SQL database. The returned object is to tied to a single
// SQL database and the object can be used to perform CRUD operations on
// the tables in the database
func (d *plugin) createDBConnection(cfg *config.SQL) (*sqlx.DB, error) {
	if cfg.DatabaseName == "" {
		return nil, errors.New("database name not provided")
	}

	if cfg.SchemaName == "" {
		return nil, errors.New("schema name not provided")
	}

	postgresqlSession, err := session.NewSession(cfg)
	if err == nil {
		return postgresqlSession.DB, nil
	}

	return nil, fmt.Errorf("unable to connect to DB, error: %v", err)
}
