package handler

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
)

func registerHealth() http.Handler {
	r := chi.NewRouter()
	r.Use(middleware.Recoverer)
	health := aohhttp.NewHealthCheck(r)
	health.AddReadinessCheck("readiness check", readyCheck())
	health.AddLivenessCheck("liveness check", liveCheck())
	return r
}

func readyCheck() func() error {
	return func() error {
		return nil
	}
}

func liveCheck() func() error {
	return func() error {
		return nil
	}
}
