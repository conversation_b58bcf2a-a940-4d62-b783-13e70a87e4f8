package demo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
	_ "time/tzdata"

	"github.com/mssfoobar/aoh-golib/temporal"
	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/mssfoobar/app/wfe/internal/wfw/activities/generic"
	"go.temporal.io/sdk/activity"
)

const getIncidentEndpoint = `%s/v1/incidents/{incidentid}`

type (
	Activities struct {
		aiModel generic.AIModel
		ims     *IMS
		hasura  *Hasura
	}

	Option struct {
		IMS    config.Ims
		GPT    config.ChatGpt
		Hasura config.Hasura
	}
)

func New(opt Option) *Activities {
	return &Activities{
		aiModel: &generic.ChatGPT{ChatGpt: opt.GPT},
		ims:     newIMS(opt.IMS.Url),
		hasura:  &Hasura{opt.Hasura},
	}
}

// SummarizeIncident call chatGPT api to summarize the given input text
func (a *Activities) SummarizeIncident(
	ctx context.Context,
	prompt string,
) (string, error) {
	value := temporal.ContextValue(ctx)
	incID, ok := value["incidentId"].(string)
	if !ok {
		return "", errors.New("missing incidentId")
	}

	// heartbeat report before long-running process
	activity.RecordHeartbeat(ctx)

	imsResp, err := a.ims.GetIncident(ctx, incID)
	if err != nil {
		return "", err
	}

	prompt = strings.Replace(prompt, "##DESCRIPTION##", imsResp.Data[0].Description, -1)
	prompt = strings.Replace(prompt, "##CREATED_AT##", imsResp.Data[0].CreatedAt, -1)
	prompt = strings.Replace(
		prompt,
		"##PRIORITY##",
		strconv.Itoa(imsResp.Data[0].Priority),
		-1,
	)
	prompt = strings.Replace(prompt, "##CONTACT##", imsResp.Data[0].Contact, -1)

	return a.aiModel.Chat(ctx, prompt)
}

// GetTripParticipants from hasura database for trip_incident demo
func (a *Activities) GetTripParticipants(ctx context.Context, endpoint string) ([]string, error) {
	value := temporal.ContextValue(ctx)
	incID, ok := value["incidentId"].(string)
	if !ok {
		return nil, errors.New("invalid input")
	}

	endpoint = strings.Replace(endpoint, ":incidentid", incID, -1)
	req, err := http.NewRequest(http.MethodGet, endpoint, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Add("x-hasura-admin-secret", a.hasura.AdminKey)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	var response []byte
	response, err = io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	var hasuraResp HasuraResponse
	err = json.Unmarshal(response, &hasuraResp)
	if err != nil {
		return nil, err
	}

	if len(hasuraResp.AohImsIncident[0].TripIncidents) == 0 {
		err := errors.New("no trip incident exist")
		return nil, err
	}

	participants := hasuraResp.AohImsIncident[0].TripIncidents[0].Trip.Participants

	var result []string
	for _, k := range participants {
		result = append(result, fmt.Sprintf("%s (%s)", k.Name, k.Contact))
	}

	return result, nil
}

type (
	IMS struct {
		getIncidentApi string
	}

	IMSResponse struct {
		Data []struct {
			Id          string `json:"id"`
			CreatedAt   string `json:"created_at"`
			Description string `json:"description"`
			Priority    int    `json:"priority"`
			Contact     string `json:"contact"`
		} `json:"data"`
	}
)

func newIMS(url string) *IMS {
	return &IMS{
		getIncidentApi: fmt.Sprintf(getIncidentEndpoint, url),
	}
}

func (i *IMS) GetIncident(ctx context.Context, id string) (*IMSResponse, error) {
	client := &http.Client{}
	endpoint := strings.Replace(i.getIncidentApi, "{incidentid}", id, -1)
	imsReq, err := http.NewRequest(http.MethodGet, endpoint, nil)
	if err != nil {
		return nil, err
	}

	// heartbeat report before long-running process
	activity.RecordHeartbeat(ctx)

	imsRes, err := client.Do(imsReq)
	if err != nil {
		return nil, err
	}

	activity.RecordHeartbeat(ctx)

	response, err := io.ReadAll(imsRes.Body)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = imsRes.Body.Close()
	}()

	var imsData IMSResponse
	if err := json.Unmarshal(response, &imsData); err != nil {
		return nil, err
	}

	if len(imsData.Data) == 0 {
		return nil, errors.New("no incident response data")
	}

	loc, err := time.LoadLocation("Asia/Singapore")
	if err != nil {
		return nil, err
	}

	t, err := time.Parse(time.RFC3339, imsData.Data[0].CreatedAt)
	if err != nil {
		return nil, err
	}

	imsData.Data[0].CreatedAt = t.In(loc).Format(time.DateTime)

	return &imsData, nil
}

type (
	Hasura struct {
		config.Hasura
	}

	HasuraResponse struct {
		AohImsIncident []struct {
			TripIncidents []struct {
				Trip struct {
					Participants []Participant `json:"participants"`
				} `json:"trip"`
			} `json:"trip__incidents"`
		} `json:"aoh_ims_incident"`
	}

	Participant struct {
		Name    string `json:"name"`
		Contact string `json:"contact"`
	}
)
