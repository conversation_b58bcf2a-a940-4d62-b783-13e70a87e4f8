package dto

import (
	"encoding/json"
)

type CreateOrUpdateSysWFActivationMilestone struct {
	TargetDuration  int              `json:"target_duration"`
	MilestoneNumber *int             `json:"milestone_number"`
	Metadata        *json.RawMessage `json:"metadata"`
}

type SysWFActivationMilestoneMetadataResponse struct {
	Actions    *[]string `json:"actions"`
	NextAction *string   `json:"next_action"`
}

type SysWFActivationMilestoneResponse struct {
	ID       string                                    `json:"id"`
	Metadata *SysWFActivationMilestoneMetadataResponse `json:"metadata"`
}
