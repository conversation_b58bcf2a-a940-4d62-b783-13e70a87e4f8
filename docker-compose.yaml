name: workflow-services

services:
  workflow-manager:
    container_name: workflow-manager
    image: 131842227326.dkr.ecr.ap-southeast-1.amazonaws.com/wfe-wfm:2.0.0
    restart: always
    environment:
      - APP_PORT=8080
      - LOG_LEVEL=debug
      - IAMS_KEYCLOAK_URL=${IAM_USERINFO_URL}
      - TEMPORAL_HOST=${TEMPORAL_HOST:-temporal}
      - TEMPORAL_PORT=${TEMPORAL_PORT:-7233}
      - TEMPORAL_NAMESPACE=${TEMPORAL_NAMESPACE:-default}
      - TEMPORAL_TASKQUEUE=${TEMPORAL_TASKQUEUE}
      - SQL_USER=${HOC_DB_USER}
      - SQL_PASSWORD=${HOC_DB_PASSWORD}
      - SQL_PLUGIN_NAME=postgres
      - SQL_HOST=${HOC_DB_HOST}
      - SQL_PORT=${HOC_DB_PORT}
      - SQL_DATABASE_NAME=${HOC_DB_DATABASE}
      - SQL_SCHEMA_NAME=aoh_wfe
      - SQL_SSL_MODE=disable
    ports:
      - "5070:8080"
    deploy:
      resources:
        reservations:
          cpus: '0.250'
          memory: 64M
        limits:
          cpus: '0.500'
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "2"
    networks:
      - temporal-network
    command: /app/workflow-manager
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health || curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
  workflow-worker:
    container_name: workflow-worker
    image: 131842227326.dkr.ecr.ap-southeast-1.amazonaws.com/wfe-wfw:2.0.0
    restart: always
    environment:
      - APP_PORT=5050
      - LOG_LEVEL=debug
      - TEMPORAL_HOST=${TEMPORAL_HOST:-temporal}
      - TEMPORAL_PORT=${TEMPORAL_PORT:-7233}
      - TEMPORAL_NAMESPACE=${TEMPORAL_NAMESPACE:-default}
      - TEMPORAL_TASKQUEUE=${TEMPORAL_TASKQUEUE}
      - TEMPORAL_WORKFLOW_TYPE=${TEMPORAL_WORKFLOW_TYPE}
      - TEMPORAL_WORKFLOW_TASKQUEUE=${TEMPORAL_WORKFLOW_TASKQUEUE}
      - TEMPORAL_ACTIVITY_TASKQUEUE=${TEMPORAL_ACTIVITY_TASKQUEUE}
      - UCS_CREATE_ROOM_ENDPOINT=${UCS_CREATE_ROOM_ENDPOINT}
      - UCS_END_ROOM_ENDPOINT=${UCS_END_ROOM_ENDPOINT}
      - UCS_BROADCAST_ROOM_ENDPOINT=${UCS_BROADCAST_ROOM_ENDPOINT}
      - IMS_GET_INCIDENT_ENDPOINT=${IMS_GET_INCIDENT_ENDPOINT}
      - SMTP_EMAIL=${SMTP_EMAIL}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - CHATGPT_URL=${CHATGPT_URL}
      - CHATGPT_API_KEY=${CHATGPT_API_KEY}
    deploy:
      resources:
        reservations:
          cpus: '0.250'
          memory: 64M
        limits:
          cpus: '0.500'
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "2"
    networks:
      - temporal-network
    command: /app/workflow-worker
  workflow-engine:
    container_name: workflow-engine
    image: 131842227326.dkr.ecr.ap-southeast-1.amazonaws.com/wfe-wfe:2.0.0
    restart: always
    environment:
      - APP_PORT=5070
      - LOG_LEVEL=debug
      - TEMPORAL_HOST=${TEMPORAL_HOST:-temporal}
      - TEMPORAL_PORT=${TEMPORAL_PORT:-7233}
      - TEMPORAL_NAMESPACE=${TEMPORAL_NAMESPACE:-default}
      - TEMPORAL_TASKQUEUE=${TEMPORAL_TASKQUEUE}
    deploy:
      resources:
        reservations:
          cpus: '0.250'
          memory: 64M
        limits:
          cpus: '0.500'
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "2"
    networks:
      - temporal-network
    command: /app/workflow-engine
  workflow-designer:
    container_name: workflow-designer
    hostname: workflow-designer
    image: 131842227326.dkr.ecr.ap-southeast-1.amazonaws.com/wfe-wfd:2.0.0
    restart: always
    environment:
    - PORT=8080
    - ORIGIN=http://wfd.${PUBLIC_DOMAIN}
    - IAM_URL=${IAM_OPENID_CFG_URL}
    - IAM_CLIENT_ID=${IAM_CLIENT_ID}
    - PUBLIC_DOMAIN=${PUBLIC_DOMAIN}
    - PUBLIC_COOKIE_PREFIX=wfd
    - OIDC_ALLOW_INSECURE_REQUESTS=1
    - LOGIN_DESTINATION=/aoh/wfd
    - LOGIN_PAGE=
    - WFM_URL=http://workflow-manager:8080
    - NODE_ENV=development
    ports:
    - 4500:8080
    deploy:
      resources:
        reservations:
          cpus: '0.250'
          memory: 64M
        limits:
          cpus: '0.500'
          memory: 128M
    logging:
      driver: "json-file"
      options:
          max-size: "10m"
          max-file: "2"
    networks:
      - temporal-network
    command: >
      build
  nginx:
    container_name: nginx-proxy
    image: nginx:latest
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - temporal-network
# === External Networks ===
networks:
  temporal-network:
    external: true
    driver: bridge
    name: temporal-network