package aohhttp

import (
	"net/http"
	"net/http/httptest"
	"strings"

	"go.uber.org/zap"
)

// BearerAuth checks if the jwt is valid by calling identity provider endpoint
//
// example url - {KeycloakBaseUrl}/realms/{Realm}/protocol/openid-connect/userinfo
func BearerAuth(url string, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			token := strings.TrimPrefix(r.Header.Get("Authorization"), "Bearer ")
			if token == "" {
				logger.Error("empty bearer token")
				w.WriteHeader(http.StatusUnauthorized)
				return
			}
			err := validateJWT(url, token)
			if err != nil {
				logger.Error("invalid bearer token", zap.Error(err))
				w.WriteHeader(http.StatusUnauthorized)
				return
			}
			next.ServeHTTP(w, r)
		})
	}
}

// RequestLogger logs the request
func RequestLogger(logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			logger.Debug(
				"request",
				zap.String("method", r.Method),
				zap.String("path", r.RequestURI),
			)
			next.ServeHTTP(w, r)
		})
	}
}

// ResponseLogger logs the response
func ResponseLogger(logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			c := httptest.NewRecorder()
			next.ServeHTTP(c, r)
			for k, v := range c.Header() {
				w.Header()[k] = v
			}
			w.WriteHeader(c.Code)

			if (c.Code >= 400) && (c.Code < 600) {
				logger.Error("response",
					zap.Int("status", c.Code),
					zap.Any("body", c.Body))
				c.Body.WriteTo(w) //nolint:errcheck
			} else {
				logger.Debug("response",
					zap.Int("status", c.Code),
					zap.Any("body", c.Body))
				c.Body.WriteTo(w) //nolint:errcheck
			}
		})
	}
}
