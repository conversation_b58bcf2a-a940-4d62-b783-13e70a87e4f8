import type { dia } from "jointjs";
import * as jointUtil from "jointjs";
import type { WorkflowData } from "$lib/aoh/wfd/stores/workflowData";
import { convert } from "./bpmn/dsl/convert/convertStates";

export type SupportedType = "workflow" | "designer" | "hybrid";

type FileTask = {
	do: boolean;
};

type ExportData = {
	name: string;
	workflow_json?: unknown;
	designer_json?: unknown;
};

export type FileType = Record<SupportedType, FileTask>;

export default class ExportFileBuilder {
	private readonly graph: dia.Graph;
	private readonly exportData: ExportData;
	private readonly fileToProcess: FileType;
	private processed: boolean = false;

	/**
	 * @param graph - jointjs graph object
	 * @param workflowData - workflow file metadata
	 */
	constructor(graph: dia.Graph, workflowData: WorkflowData) {
		this.graph = graph;
		this.exportData = {
			name: workflowData.name,
		};

		const defaultFileTask = {
			do: false,
		};

		this.fileToProcess = {
			workflow: jointUtil.util.cloneDeep(defaultFileTask),
			designer: jointUtil.util.cloneDeep(defaultFileTask),
			hybrid: jointUtil.util.cloneDeep(defaultFileTask),
		};
	}

	/**
	 * Add a file to list of files to process
	 * @param type - type of file to process
	 * @returns an instance of file builder (to use for chaining)
	 */
	addSchema(type: SupportedType): this {
		this.fileToProcess[type].do = true;
		return this;
	}

	/**
	 * Get processed data without generating file blobs
	 * Requires running processFiles first
	 * @returns export data object
	 */
	getExportData() {
		if (!this.processed) {
			throw new Error("Cannot get export data. Please run processFile() first.");
		}

		return this.exportData;
	}

	private getFileName(fileExtension: string) {
		return `${this.exportData.name}.${fileExtension}`;
	}

	/**
	 * Process files added via addSchema.
	 * Required to run before running getExportData or generateBlob
	 * */
	processFiles(): this {
		// Schema or Hybrid
		if (this.fileToProcess.workflow.do || this.fileToProcess.hybrid.do) {
			const metadata = {
				specVersion: "2.0",
				name: this.exportData.name,
			};
			this.exportData.workflow_json = convert(this.graph, metadata);
		}

		// Designer or Hybrid
		if (this.fileToProcess.designer.do || this.fileToProcess.hybrid.do) {
			const designerData = this.graph.toJSON();
			designerData.name = this.exportData.name;

			this.exportData.designer_json = designerData;
		}

		this.processed = true;
		return this;
	}

	/**
	 * Generate file blob for download.
	 * Requires running processFiles first.
	 * @returns export file data as blob, or a zip file blob if the number of files is more than 1
	 */
	async generateBlob(): Promise<{ fileName: string; blob: Blob } | null> {
		if (!this.processed) {
			throw new Error("Cannot generate blob. Please run processFile() first.");
		}

		const fileData: { fileName: string; data: string }[] = [];

		// Add file to zip
		Object.entries(this.fileToProcess).forEach(([_fileType, fileTask]) => {
			if (!fileTask.do) {
				return;
			}

			const stringData = JSON.stringify(this.exportData);
			fileData.push({
				fileName: this.getFileName("json"),
				data: stringData,
			});
		});

		if (fileData.length === 1) {
			const blob = new Blob([fileData[0].data], {
				type: "text/plain",
			});
			return { blob, fileName: fileData[0].fileName };
		}

		return null;
	}
}
