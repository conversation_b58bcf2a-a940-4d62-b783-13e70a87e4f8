<script lang="ts" context="module">
	import { browser } from "$app/environment";
	import { onMount } from "svelte";
	import { darkModeStore } from "./theme";
	import { get } from "svelte/store";

	export function isBrowserDarkMode(): boolean {
		return browser ? document.documentElement.classList.contains("dark") : false;
	}

	export function isDarkMode(): boolean {
		return get(darkModeStore);
	}

	//INFO: If you want to change the theme mode, you may use this function
	export function setDarkMode(isDarkMode: boolean) {
		let className: string = isDarkMode ? "dark" : "base";
		localStorage.setItem("theme", isDarkMode === true ? "dark" : "light");

		if (browser) {
			let element: HTMLElement = document.documentElement;
			element.classList.remove("invisible");
			element.className = className;
			darkModeStore.set(isDarkMode);
		}
	}
</script>

<script lang="ts">
	onMount(() => {
		setDarkMode(localStorage.getItem("theme") === "dark");
	});
</script>

<slot />
