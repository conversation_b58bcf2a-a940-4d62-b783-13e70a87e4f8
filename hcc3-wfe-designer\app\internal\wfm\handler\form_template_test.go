package handler

import (
	"bytes"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/mssfoobar/app/wfe/internal/model"
	mm "github.com/mssfoobar/app/wfe/internal/model/mock"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

var (
	testFormTemplateReq = formTemplateReqBody{
		Name:     "testFormTemplate",
		FormJson: []byte(`{"components": [{"name": "textfield"}, {"action": "submit"}]}`),
	}

	testFormTemplateResp = model.FormTemplateResponse{
		Id:       testId,
		Name:     testFormTemplateReq.Name,
		FormJson: testFormTemplateReq.FormJson,
	}
)

func TestRegisterFormTemplate(t *testing.T) {
	factory := model.Factory{}
	handler := RegisterFormTemplate(&factory, "http://localhost:8080")

	req, err := http.NewRequest(http.MethodPut, "/save", nil)
	if err != nil {
		t.Fatal(err)
	}

	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)

	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestFormTemplate_Save(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockFormTemplateStore(ctrl)
	f := FormTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/save",
			bytes.NewReader(util.StructToBytes(testFormTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().SaveFormTemplate(gomock.Any(), gomock.Any()).Return(&testFormTemplateResp, nil)
		f.Save(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToMap(testFormTemplateResp), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid request body, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/save",
			bytes.NewReader(util.StructToBytes(formTemplateReqBody{})),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		f.Save(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/save",
			bytes.NewReader(util.StructToBytes(testFormTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		f.Save(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPut,
			"/save",
			bytes.NewReader(util.StructToBytes(testFormTemplateReq)),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().SaveFormTemplate(gomock.Any(), gomock.Any()).Return(nil, errors.New("test error"))
		f.Save(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestFormTemplate_List(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockFormTemplateStore(ctrl)
	f := FormTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			ListFormTemplate(gomock.Any(), gomock.Any()).
			Return(&model.ListFormTemplateResponse{
				FormTemplates: []model.FormTemplateResponse{testFormTemplateResp},
				TotalCount:    0,
			}, nil)
		f.List(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToSlice([]any{testFormTemplateResp}), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/?page=a&size=b", nil)
		r.Header.Add("Content-Type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		f.List(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		f.List(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			ListFormTemplate(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("test error"))
		f.List(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestFormTemplate_Get(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockFormTemplateStore(ctrl)
	f := FormTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			GetFormTemplate(gomock.Any(), gomock.Any()).
			Return(&testFormTemplateResp, nil)
		f.Get(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToMap(testFormTemplateResp), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{template_id}", nil),
			map[string]string{"template_id": "123"})
		r.Header.Add("Content-Type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		f.Get(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		f.Get(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.
			EXPECT().
			GetFormTemplate(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("test error"))
		f.Get(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestFormTemplate_Delete(t *testing.T) {
	ctrl := gomock.NewController(t)
	mStore := mm.NewMockFormTemplateStore(ctrl)
	f := FormTemplate{
		store: mStore,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodDelete, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().DeleteFormTemplate(gomock.Any(), gomock.Any()).Return(nil)
		f.Delete(w, r)
		require.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodDelete, "/{template_id}", nil),
			map[string]string{"template_id": "123"})
		r.Header.Add("Content-Type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		f.Delete(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodDelete, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		f.Delete(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodDelete, "/{template_id}", nil),
			map[string]string{"template_id": testId.String()})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mStore.EXPECT().DeleteFormTemplate(gomock.Any(), gomock.Any()).Return(errors.New("test error"))
		f.Delete(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}
