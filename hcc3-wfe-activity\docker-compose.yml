#
# This is the docker-compose file for the hcc3-wfe-activities service.
# It defines the configuration for the service container, including the image, build settings, environment variables, and ports.
# The container name is set using the SERVICE_NAME environment variable.
# The image is built using the Dockerfile located at ./docker/service.Dockerfile.
# The environment variables are loaded from the .env file.
# The service is exposed on the specified APP_PORT.
# 
# Example usage:
# docker-compose up -d
#
# Note: Make sure to replace the placeholders (e.g., ${SERVICE_NAME}, ${TAG}, ${APP_PORT}) with actual values before running the docker-compose command.
services:
    hcc3-wfe-activities:
        container_name: ${SERVICE_NAME}
        image: ${SERVICE_NAME}:${TAG}
        build:
            dockerfile: ./docker/worker.Dockerfile
            context: .
        env_file:
            - .env
        environment:
            - APP_PORT=${APP_PORT}
            - LOG_LEVEL=${LOG_LEVEL}
            - TEMPORAL_HOST=${TEMPORAL_HOST}
            - TEMPORAL_PORT=${TEMPORAL_PORT}
            - TEMPORAL_NAMESPACE=${TEMPORAL_NAMESPACE}
            - TEMPORAL_WORKFLOW_TYPE=${TEMPORAL_WORKFLOW_TYPE}
            - TEMPORAL_WORKFLOW_TASKQUEUE=${TEMPORAL_WORKFLOW_TASKQUEUE}
            - TEMPORAL_ACTIVITY_TASKQUEUE=${TEMPORAL_ACTIVITY_TASKQUEUE}
            - DATA_AGG_URL=${DATA_AGG_URL}
            - SOP_URL=${SOP_URL}
            - COMMON_URL=${COMMON_URL}
        ports:
            - ${APP_PORT}:${APP_PORT}
        networks:
            - temporal-network
# === Network ===
networks:
  temporal-network:
    driver: bridge
    name: aoh-network
    external: true