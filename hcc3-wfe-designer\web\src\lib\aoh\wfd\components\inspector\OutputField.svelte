<script lang="ts">
	import { dia, ui } from "rappid/rappid";
	import { Input } from "$lib/aoh/wfd/components/ui/input/index.js";
	import { Label } from "$lib/aoh/wfd/components/ui/label/index.js";
	import { RESULT_REGEX } from "$lib/aoh/wfd/components/inspector/constant.js";

	let { inspector }: { inspector: ui.Inspector } = $props();

	const cell = inspector.options.cell as dia.Cell;

	const formatVariableName = (name: string): string => {
		return `Result_${name.replace(RESULT_REGEX, "")}`;
	};

	let value = $state(formatVariableName(cell.attr("label/text")));
	let type = cell.attr("data/resultType");

	$effect(() => {
		cell.attr("data/result", value);
	});
</script>

<Label for="name">Variable Name</Label>
<Input id="name" bind:value readonly />
{#if type}
	<Label for="type">Variable Type</Label>
	<Input id="type" value={type} readonly />
{/if}
