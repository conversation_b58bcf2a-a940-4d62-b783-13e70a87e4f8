package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"mssfoobar/wfe_activity/internal/config"
	"mssfoobar/wfe_activity/internal/worker"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"go.uber.org/zap"
)

func main() {
	var envFile string
	flag.StringVar(&envFile, "e", "./.env", "environment file")
	flag.Parse()

	if envFile == "" {
		flag.PrintDefaults()
		return
	}

	if err := config.Init(envFile); err != nil {
		aohlog.Fatal("unable to initialize config", zap.Error(err))
	}

	cfg := config.AppConfig
	if cfg.LogLevel == "debug" {
		aohlog.SetDevelopment()
	}

	w := worker.New()
	w.Start(*cfg)

	defer w.Stop()

	// Press Ctrl+C to exit the process
	quitCh := make(chan os.Signal, 1)
	signal.Notify(quitCh, os.Interrupt, syscall.SIGTERM)
	<-quitCh
}
