package util

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"mssfoobar/wfe_activity/internal/worker/dto"
	"net/http"
	"os"
	"strings"
)

func GetMilestoneAction(milestone *dto.SysWFActivationMilestoneResponse) string {
	if milestone.Metadata == nil || milestone.Metadata.NextAction == nil {
		return ""
	}
	return *milestone.Metadata.NextAction
}

func GetAccessTokenFromCommon() (string, error) {
	var COMMON_URL = os.Getenv("COMMON_URL")
	getTokenUrl := fmt.Sprintf("%s/iams/token?scope=openid", COMMON_URL)

	response, err := ExecuteAPI(getTokenUrl, "GET", nil)
	if err != nil {
		fmt.Println("Error getting access token:", err)
		return "", err
	}

	// Parse and cache token
	var tokenResp dto.TokenResponse
	if err := json.Unmarshal(response, &tokenResp); err != nil {
		return "", fmt.Errorf("token parse failed: %w", err)
	}

	return tokenResp.AccessToken, nil
}

func ExecuteAPI(url string, typeRequest string, body io.Reader) ([]byte, error) {
	req, err := http.NewRequest(typeRequest, url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Referer", url)
	req.Header.Set("Origin", url)
	var defaultClient = http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{},
		},
	}
	resp, err := defaultClient.Do(req)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("API returned status code %d", resp.StatusCode)
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return respBody, nil
}

func ExecuteAPIWithAuth(url string, typeRequest string, body io.Reader, authToken string) ([]byte, error) {
	req, err := http.NewRequest(typeRequest, url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Referer", url)
	req.Header.Set("Origin", url)
	req.Header.Set("Content-Type", "application/json")

	// Add Authorization header if token is provided
	if authToken != "" {
		req.Header.Set("Authorization", "Bearer "+authToken)
	}

	var defaultClient = http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{},
		},
	}
	resp, err := defaultClient.Do(req)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("API returned status code %d", resp.StatusCode)
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return respBody, nil
}

func ConvertToTitleCase(str string) string {
	// Split the string by underscores
	words := strings.Split(str, "_")
	for i, word := range words {
		// Capitalize the first letter of each word
		words[i] = strings.Title(word)
	}
	// Join the words back together with spaces
	return strings.Join(words, " ")
}

func GetUsernamesAndEmails(recipients string) ([]string, []string) {
	if recipients == "" {
		return nil, nil
	}

	var usernames []string
	var emails []string
	for _, value := range strings.Split(strings.TrimSpace(recipients), ",") {
		value = strings.TrimSpace(value)
		if value == "" {
			continue
		}

		// Case value is not valid => assume it as an email
		openParenIndex := strings.LastIndex(value, "(")
		if openParenIndex == -1 {
			emails = append(emails, value)
			continue
		}
		closeParenIndex := strings.LastIndex(value, ")")
		if closeParenIndex == -1 || closeParenIndex < openParenIndex {
			emails = append(emails, value)
			continue
		}

		// Extract the username and email
		username := strings.TrimSpace(value[:openParenIndex])
		email := strings.TrimSpace(value[openParenIndex+1 : closeParenIndex])
		usernames = append(usernames, username)
		emails = append(emails, email)
	}
	return usernames, emails
}

func GetUsernamesAndIds(recipientUsers string) ([]string, []string) {
	if recipientUsers == "" {
		return nil, nil
	}

	var usernames []string
	var ids []string
	for _, value := range strings.Split(strings.TrimSpace(recipientUsers), ",") {
		value = strings.TrimSpace(value)
		if value == "" {
			continue
		}

		// Split by colon to separate id and username
		parts := strings.SplitN(value, ":", 2)
		if len(parts) != 2 {
			// Case value is not valid => assume it as an id
			ids = append(ids, value)
			continue
		}

		id := strings.TrimSpace(parts[0])
		username := strings.TrimSpace(parts[1])

		if id != "" && username != "" {
			ids = append(ids, id)
			usernames = append(usernames, username)
		}
	}
	return usernames, ids
}

func UpdateAssignee(
	workflowId string,
	recipientUsernames []string,
	ccRecipientUsernames []string,
	bccRecipientUsernames []string,
	c3RecipientUsernames []string,
	c3CcRecipientUsernames []string,
	c3BccRecipientUsernames []string,
) error {
	var SOP_URL = os.Getenv("SOP_URL")

	allUsernames := []string{}
	if recipientUsernames != nil {
		allUsernames = append(allUsernames, recipientUsernames...)
	}
	if ccRecipientUsernames != nil {
		allUsernames = append(allUsernames, ccRecipientUsernames...)
	}
	if bccRecipientUsernames != nil {
		allUsernames = append(allUsernames, bccRecipientUsernames...)
	}
	if c3RecipientUsernames != nil {
		allUsernames = append(allUsernames, c3RecipientUsernames...)
	}
	if c3CcRecipientUsernames != nil {
		allUsernames = append(allUsernames, c3CcRecipientUsernames...)
	}
	if c3BccRecipientUsernames != nil {
		allUsernames = append(allUsernames, c3BccRecipientUsernames...)
	}

	assigneeData, err := json.Marshal(dto.UpdateSysWFActivationAssignee{
		Assignees: allUsernames,
	})
	if err != nil {
		return err
	}
	_, err = ExecuteAPI(fmt.Sprintf("%s/sys-wf-activation/%s/assignee", SOP_URL, workflowId), "POST", bytes.NewBuffer(assigneeData))
	if err != nil {
		return err
	}

	return nil
}

func TrimStringSlice(slice []string) []string {
	result := make([]string, len(slice))
	for i, str := range slice {
		result[i] = strings.TrimSpace(str)
	}
	return result
}
