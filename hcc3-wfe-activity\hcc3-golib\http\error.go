package aohhttp

import (
	"fmt"
	"net/http"
	"time"

	"github.com/go-chi/render"
)

type errorMsg struct {
	Message string `json:"message"`
}

type ErrorResponse struct {
	HttpStatusCode int        `json:"-"`
	Data           any        `json:"data,omitempty"`
	Message        string     `json:"message,omitempty"`
	SentAt         string     `json:"sent_at,omitempty"`
	Errors         []errorMsg `json:"errors,omitempty"`
}

func (e *ErrorResponse) Error() string {
	return fmt.Sprint(e.Errors)
}

func (e *ErrorResponse) Render(w http.ResponseWriter, r *http.Request) error {
	render.Status(r, e.HttpStatusCode)
	return nil
}

func ErrResponse(code int, message string, errors []error) render.Renderer {
	var errMsg []errorMsg
	for _, v := range errors {
		errMsg = append(errMsg, errorMsg{Message: v.Error()})
	}
	return &ErrorResponse{
		HttpStatusCode: code,
		Message:        message,
		SentAt:         time.Now().UTC().Format(time.RFC3339),
		Errors:         errMsg,
	}
}

func ErrResponseWithData(code int, message string, data any, errors []error) render.Renderer {
	var errMsg []errorMsg
	for _, v := range errors {
		errMsg = append(errMsg, errorMsg{Message: v.Error()})
	}
	return &ErrorResponse{
		HttpStatusCode: code,
		Data:           data,
		Message:        message,
		SentAt:         time.Now().UTC().Format(time.RFC3339),
		Errors:         errMsg,
	}
}
