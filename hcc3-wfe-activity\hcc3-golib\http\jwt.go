package aohhttp

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/golang-jwt/jwt/v4"
)

var ErrEmptyTenant = errors.New("empty active tenant")

type JwtClaim struct {
	Id           string `json:"sub"`
	Name         string `json:"name"`
	ActiveTenant struct {
		Id   string `json:"tenant_id"`
		Name string `json:"tenant_name"`
	} `json:"active_tenant"`
	ResourceAccess map[string]any `json:"resource_access"`
}

// GetJWTClaim extracts the JWT from the request.
// This parse the jwt into a JwtClaim struct and check if it has active tenant id.
// Return error on invalid JwtClaim format or empty active tenant id.
//
// If the jwt is not in JwtClaim format, use ParseJWT instead.
func GetJWTClaim(r *http.Request) (*JwtClaim, error) {
	token, err := ParseJWT(r)
	if err != nil {
		return nil, err
	}

	raw, err := json.Marshal(token)
	if err != nil {
		return nil, err
	}

	var claim JwtClaim
	err = json.Unmarshal(raw, &claim)
	if err != nil {
		return nil, err
	}

	if claim.ActiveTenant.Id == "" {
		return nil, ErrEmptyTenant
	}

	return &claim, nil
}

// ParseJWT extracts the JWT from the request and parse it into a map[string]any.
func ParseJWT(r *http.Request) (map[string]any, error) {
	token := strings.TrimPrefix(r.Header.Get("Authorization"), "Bearer ")

	claims := jwt.MapClaims{}
	_, _, err := jwt.NewParser().ParseUnverified(token, claims)
	if err != nil {
		return nil, fmt.Errorf("failed to parse jwt: %w", err)
	}

	return claims, nil
}

func validateJWT(url string, token string) error {
	client := http.Client{}
	r, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return err
	}
	r.Header.Set("Authorization", "Bearer "+token)
	resp, err := client.Do(r)
	if err != nil {
		return err
	}
	defer resp.Body.Close() //nolint:errcheck
	if resp.StatusCode != 200 {
		b, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("status code: %d; response body: %s", resp.StatusCode, string(b))
	}
	return nil
}
