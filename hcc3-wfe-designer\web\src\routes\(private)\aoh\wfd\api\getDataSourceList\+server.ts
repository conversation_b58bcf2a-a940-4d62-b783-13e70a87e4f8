import { json, error } from "@sveltejs/kit";
import type { RequestHandler } from "@sveltejs/kit";
import { StatusCodes } from "http-status-codes";
import { env } from "$env/dynamic/private";

export const GET: RequestHandler = async ({ locals }) => {
	if (!locals.authResult.success) {
		return new Response(
			JSON.stringify({
				message: "Unauthorized",
			}),
			{
				status: StatusCodes.UNAUTHORIZED,
			}
		);
	}

	const tenant_id = locals?.authResult?.claims?.active_tenant?.tenant_id || "";

	const bearerAuthorizationString = `Bearer ${locals.authResult.access_token}`;

	const headers = {
		"Content-Type": "application/json",
		Authorization: bearerAuthorizationString,
	};

	const data_agg_promise = await fetch(
		env.DATA_AGG_URL +
			`/sys-datasource/getdatasourcesbymaincategory?tenantid=${tenant_id}&maincategory=Aggregated Data Source,SOP Parameter&include_inactive=true`,
		{
			method: "GET",
			headers,
		}
	);

	if (!data_agg_promise.ok) {
		return error(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to fetch IAMS data");
	}

	const data = await data_agg_promise.json();
	return json(data);
};
