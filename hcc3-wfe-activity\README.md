# Workflow Engine Activity

Template Repository for Workflow Engine Activities Development.

## Folder Structure

This repo have the following folder structure:

```
.
├── cmd
│   └── worker
│       └── main.go
├── go.mod
├── go.sum
├── internal
│   ├── config
│   │   └── config.go
│   └── worker
│       ├── activities
│       │   └── activities.go
│       ├── handler
│       │   └── health.go
│       └── worker.go
├── README.md
├── sample
│   └── helloworld
│       ├── activity.go
│       ├── README.md
│       ├── starter
│       │   └── main.go
│       ├── worker
│       │   └── main.go
│       └── workflow.go
└── vendor
    └── modules.txt
```

- `cmd/worker` - contains the main function to start the worker
- `internal/config` - contains the configuration for the worker. here you can add additional environment variables
- `internal/worker/worker` - contains the out-of-the-box activities registration to Temporal server
- `internal/worker/activities` - contains the activities' implementation. here you can add your custom activities
- `internal/worker/handler` - contains the kubernetes health check implementation. delete if you don't need it
- `sample/helloworld` - contains the sample activity implementation
- `vendor/modules.txt` - contains the list of downloaded dependencies


## Activity Development

Write your own activity inside the `internal/worker/activities` folder
```go
type Activities struct{}

// Add your own activities here with Activities as the receiver
// 
// for example
// func (a *Activities) ActivityName(ctx context.Context, input string) (string, error) {
// }
```

## Building an image

Build an image using the docker build command. GitHub personal access token must have the `read:packages` scope to 
download private AOH packages.
```
docker build --build-arg GITHUB_PAT={YOUR_GITHUB_PAT} -f ./docker/worker.Dockerfile .
## Environment Variables

| Name                 | Default     | Description                                                                                                       |
|----------------------|-------------|-------------------------------------------------------------------------------------------------------------------|
| `APP_PORT`           | `'5000'`    | Application port for serving HTTP requests.                                                                       |
| `LOG_LEVEL`          | `'info'`    | The logging level. `info` for production, `debug` for development.                                                |
| `TEMPORAL_HOST`      | `localhost` | The hostname of the temporal server.                                                                              |
| `TEMPORAL_PORT`      | `7233`      | The port number of the temporal server.                                                                           |
| `TEMPORAL_NAMESPACE` | `default`   | The namespace of the temporal server to scope workflow & activity execution. Must be same across all WFE modules. |
| `TEMPORAL_TASKQUEUE` | `taskqueue` | The name of the workflow and activity execution task queue. Must be same across all WFE modules.                  |

## Building an image

Build an image using the docker build command.
```
docker build -f ./docker/worker.Dockerfile .
```
