package main

import (
	"log"

	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/worker"
	"mssfoobar/wfe_activity/sample/helloworld"
)

func main() {
	// The client and worker are heavyweight objects that should be created once per process.
	c, err := client.Dial(client.Options{})
	if err != nil {
		log.<PERSON>alln("Unable to create client", err)
	}
	defer c.Close()

	w := worker.New(c, "hello-world", worker.Options{})

	w.<PERSON>ork<PERSON>(helloworld.Workflow)
	w.RegisterActivity(&helloworld.Activities{})
	err = w.<PERSON>(worker.InterruptCh())
	if err != nil {
		log.<PERSON>alln("Unable to start worker", err)
	}
}
