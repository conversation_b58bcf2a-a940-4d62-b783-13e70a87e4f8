package postgresql

import (
	"fmt"

	"github.com/google/uuid"
)

const (
	readSchemaVersionQuery = `SELECT value FROM module_info WHERE key = 'current_schema_version' LIMIT 1;`

	writeSchemaVersionQuery = `INSERT INTO module_info (id, key, value) VALUES ($1, 'current_schema_version', $2) ON CONFLICT (key) DO UPDATE SET value = $2, updated_at = now();`

	createModuleInfoTableQuery = `CREATE TABLE IF NOT EXISTS module_info (
    								id UUID NOT NULL,
    								key TEXT NOT NULL UNIQUE,
									value TEXT NOT NULL,
									comment TEXT NULL,
    								created_at TIMESTAMP NOT NULL DEFAULT now(),
    								updated_at TIMESTAMP NOT NULL DEFAULT now(),
    								PRIMARY KEY (id));`

	listTablesQuery = `SELECT table_name FROM information_schema.tables WHERE table_schema = $1 AND table_type = 'BASE TABLE';`

	dropTableQuery = "DROP TABLE %v CASCADE"
)

func (pdb *db) CreateModuleInfoTable() error {
	return pdb.Exec(createModuleInfoTableQuery)
}

func (pdb *db) ReadSchemaVersion() (string, error) {
	var version string
	err := pdb.db.Get(&version, readSchemaVersionQuery)
	return version, err
}

func (pdb *db) UpdateSchemaVersion(version string) error {
	return pdb.Exec(writeSchemaVersionQuery, uuid.New().String(), version)
}

func (pdb *db) ListTables() ([]string, error) {
	var tables []string
	err := pdb.db.Select(&tables, listTablesQuery, pdb.SchemaName())
	return tables, err
}

func (pdb *db) DropTable(table string) error {
	return pdb.Exec(fmt.Sprintf(dropTableQuery, table))
}

func (pdb *db) DropAllTables() error {
	tables, err := pdb.ListTables()
	if err != nil {
		return err
	}

	for _, table := range tables {
		err = pdb.DropTable(table)
		if err != nil {
			return err
		}
	}
	return nil
}

func (pdb *db) Exec(stmt string, args ...interface{}) error {
	_, err := pdb.db.Exec(stmt, args...)
	return err
}
