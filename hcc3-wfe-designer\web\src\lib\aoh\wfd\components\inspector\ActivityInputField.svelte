<script lang="ts">
	import type { dia, ui } from "rappid/rappid";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { activityStore } from "$lib/aoh/wfd/stores/activities";
	import { eventStore } from "$lib/aoh/wfd/stores/events";
	import { Event as EventShape } from "$lib/aoh/wfd/bpmn/shapes";
	import { Toggle } from "$lib/aoh/wfd/components/ui/toggle";
	import type { Activity } from "$lib/aoh/wfd/activitySDK/api";
	import ExpressionField from "$lib/aoh/wfd/activitySDK/components/ExpressionField.svelte";
	import { Textarea } from "$lib/aoh/wfd/components/ui/textarea";
	import { Input } from "$lib/aoh/wfd/components/ui/input";

	let { inspector, activity }: { inspector: ui.Inspector; activity: Activity } = $props();

	const cell = inspector.options.cell as dia.Cell;
	const isEventElement = cell.constructor === EventShape;
	const typeName = cell.attr("type/text");
	let values: Record<string, string> = $state(cell.attr("data/typeOptions") || {});
	let activityVariables: Record<string, boolean> = $state(cell.attr("data/activityVariables") || {});

	const paramList: Record<string, unknown>[] | undefined = isEventElement
		? $eventStore.find((f) => f.event_type === typeName)?.event_param
		: $activityStore.find((f) => f.activity_type === typeName)?.activity_param;

	if (paramList) {
		for (const param of paramList) {
			const name = Object.keys(param)[0];
			if (values[name] === undefined) {
				values[name] = "";
			}
			if (activityVariables[name] === undefined) {
				activityVariables[name] = false;
			}
		}
	}

	const onValueChange = () => {
		cell.attr("data/typeOptions", values);
	};

	const toggleExpr = (name: string) => {
		cell.attr("data/typeOptions/" + name, "");
		cell.attr("data/activityVariables/" + name, activityVariables[name]);
	};
</script>

{#if paramList}
	{#each paramList as param}
		{@const name = Object.keys(param)[0]}
		{@const value = param[name]}
		{@const type = typeof value}

		<div class="flex items-center justify-between">
			<Label>{name}</Label>
			<Toggle
				bind:pressed={activityVariables[name]}
				onPressedChange={() => toggleExpr(name)}
				variant="outline"
				class="italic size-6"
			>
				Expr
			</Toggle>
		</div>
		{#if activityVariables[name]}
			<ExpressionField {activity} {name} />
		{:else if type === "string" || type === "object"}
			<Textarea class="min-w-full min-h-10" bind:value={values[name]} onblur={onValueChange} spellcheck="false" />
		{:else if type === "number"}
			<Input type="number" onblur={onValueChange} bind:value={values[name]} />
		{:else}
			<p class="text-destructive">Unsupported type</p>
		{/if}
	{/each}
{/if}
