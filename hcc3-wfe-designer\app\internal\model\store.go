package model

import (
	"fmt"

	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

var supportedPlugins = map[string]sqlplugin.Plugin{}

// RegisterPlugin will register a SQL plugin
func RegisterPlugin(pluginName string, plugin sqlplugin.Plugin) {
	if _, ok := supportedPlugins[pluginName]; ok {
		panic("plugin " + pluginName + " already registered")
	}
	supportedPlugins[pluginName] = plugin
}

// NewSQLDB creates a returns a reference to a logical connection to the
// underlying SQL database. The returned object is to tied to a single
// SQL database and the object can be used to perform CRUD operations on
// the tables in the database
func NewSQLDB(cfg *config.SQL) (sqlplugin.DB, error) {
	plugin, ok := supportedPlugins[cfg.PluginName]
	if !ok {
		return nil, fmt.Errorf(
			"not supported plugin %v, only supported: %v",
			cfg.PluginName,
			supportedPlugins,
		)
	}

	return plugin.CreateDB(cfg)
}

// NewModuleInfoDB returns a module info db
func NewModuleInfoDB(cfg *config.SQL) (sqlplugin.ModuleInfoDB, error) {
	plugin, ok := supportedPlugins[cfg.PluginName]
	if !ok {
		return nil, fmt.Errorf(
			"not supported plugin %v, only supported: %v",
			cfg.PluginName,
			supportedPlugins,
		)
	}

	return plugin.CreateModuleInfoDB(cfg)
}
