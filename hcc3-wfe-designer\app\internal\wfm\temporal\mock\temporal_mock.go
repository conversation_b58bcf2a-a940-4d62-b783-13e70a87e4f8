// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go
//
// Generated by this command:
//
//	mockgen -source=interface.go -destination=mock/temporal_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	json "encoding/json"
	reflect "reflect"

	temporal "github.com/mssfoobar/app/wfe/internal/wfm/temporal"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// ExecuteWorkflow mocks base method.
func (m *MockService) ExecuteWorkflow(ctx context.Context, workflowSchema json.RawMessage, metadata map[string]any) (*temporal.WorkflowRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteWorkflow", ctx, workflowSchema, metadata)
	ret0, _ := ret[0].(*temporal.WorkflowRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteWorkflow indicates an expected call of ExecuteWorkflow.
func (mr *MockServiceMockRecorder) ExecuteWorkflow(ctx, workflowSchema, metadata any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteWorkflow", reflect.TypeOf((*MockService)(nil).ExecuteWorkflow), ctx, workflowSchema, metadata)
}

// GetWorkflowHistory mocks base method.
func (m *MockService) GetWorkflowHistory(ctx context.Context, id string, page, size int, desc bool) ([]temporal.HistoryEvent, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowHistory", ctx, id, page, size, desc)
	ret0, _ := ret[0].([]temporal.HistoryEvent)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetWorkflowHistory indicates an expected call of GetWorkflowHistory.
func (mr *MockServiceMockRecorder) GetWorkflowHistory(ctx, id, page, size, desc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowHistory", reflect.TypeOf((*MockService)(nil).GetWorkflowHistory), ctx, id, page, size, desc)
}

// ListOpenWorkflow mocks base method.
func (m *MockService) ListOpenWorkflow(ctx context.Context, page, size int, asc bool) ([]temporal.WorkflowExecution, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOpenWorkflow", ctx, page, size, asc)
	ret0, _ := ret[0].([]temporal.WorkflowExecution)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListOpenWorkflow indicates an expected call of ListOpenWorkflow.
func (mr *MockServiceMockRecorder) ListOpenWorkflow(ctx, page, size, asc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOpenWorkflow", reflect.TypeOf((*MockService)(nil).ListOpenWorkflow), ctx, page, size, asc)
}

// SignalWorkflow mocks base method.
func (m *MockService) SignalWorkflow(ctx context.Context, workflowId, signalName string, data any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignalWorkflow", ctx, workflowId, signalName, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// SignalWorkflow indicates an expected call of SignalWorkflow.
func (mr *MockServiceMockRecorder) SignalWorkflow(ctx, workflowId, signalName, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignalWorkflow", reflect.TypeOf((*MockService)(nil).SignalWorkflow), ctx, workflowId, signalName, data)
}

// TerminateWorkflow mocks base method.
func (m *MockService) TerminateWorkflow(ctx context.Context, workflowId, reason string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TerminateWorkflow", ctx, workflowId, reason)
	ret0, _ := ret[0].(error)
	return ret0
}

// TerminateWorkflow indicates an expected call of TerminateWorkflow.
func (mr *MockServiceMockRecorder) TerminateWorkflow(ctx, workflowId, reason any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TerminateWorkflow", reflect.TypeOf((*MockService)(nil).TerminateWorkflow), ctx, workflowId, reason)
}
