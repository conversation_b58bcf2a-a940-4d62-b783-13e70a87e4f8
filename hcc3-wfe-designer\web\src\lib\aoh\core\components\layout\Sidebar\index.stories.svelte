<!-- More on how to set up stories at: https://storybook.js.org/docs/writing-stories -->

<script module lang="ts">
	import { defineMeta } from "@storybook/addon-svelte-csf";
	import Sidebar from "./index.svelte";
	import type { SidebarItem } from "./index.svelte";

	const sidebarItems: SidebarItem[] = [
		{
			icon: "iconify mdi--house",
			label: "House",
			href: "/_example",
		},
	];

	const { Story } = defineMeta({
		title: "AOH/Core/Sidebar",
		component: Sidebar,
		tags: ["autodocs"],
		argTypes: {
			items: {
				control: "object",
				description: "A menu item in the sidebar",
				mapping: sidebarItems,
			},
		},
		args: {
			items: sidebarItems,
		},
	});
</script>

<Story name="Primary" />
