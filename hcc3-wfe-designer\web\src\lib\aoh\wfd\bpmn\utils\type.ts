export class Queue<T> {
	private readonly queue: T[];
	private readonly capacity: number;

	constructor(length: number = Infinity) {
		if (length !== Infinity) {
			this.queue = new Array<T>(length);
		} else {
			this.queue = new Array<T>();
		}

		this.capacity = length;
	}

	/**
	 * Enqueue elements
	 * @param items - list of items to enqueue
	 */
	enqueue(...items: T[]): void {
		if (this.queue.length === this.capacity) {
			throw new Error("Queue max capacity reached.");
		}

		this.queue.push(...items);
	}

	/**
	 * Dequeue an element
	 * @returns dequeued element or undefined if queue is empty
	 */
	dequeue(): T | undefined {
		return this.queue.shift();
	}

	/**
	 * Get length of queue
	 * @returns number of elements in queue
	 */
	get length() {
		return this.queue.length;
	}
}

/**
 * Bidirectional dictionary with ordered number left-side
 */
export class OrderedNumBiDict<T> {
	private readonly mapByKey: Map<number, T>;
	private readonly mapByVal: Map<T, number>;

	*[Symbol.iterator]() {
		yield* [...this.mapByKey.entries()].sort((a, b) => a[0] - b[0]);
	}

	constructor() {
		this.mapByKey = new Map<number, T>();
		this.mapByVal = new Map<T, number>();
	}

	/**
	 * Get dict size
	 * @returns number of elements in dict
	 */
	get size(): number {
		return this.mapByKey.size;
	}

	/**
	 * Set value to dict
	 * @param key - dict key (left side)
	 * @param value - dict value (right side)
	 */
	set(key: number, value: T) {
		this.mapByKey.set(key, value);
		this.mapByVal.set(value, key);
	}

	/**
	 * Get element by key (left side value)
	 * @param key - left side value
	 * @returns value corresponding to key
	 */
	getByNum(key: number): T | undefined {
		return this.mapByKey.get(key);
	}

	/**
	 * Get element by value (right side value)
	 * @param value - left side value
	 * @returns key corresponding to value
	 */
	getByVal(value: T): number | undefined {
		return this.mapByVal.get(value);
	}

	/**
	 * Check if key exists in dict
	 * @param key - Check if key exists in dict
	 * @returns true if key exists and false otherwise
	 */
	hasNum(key: number): boolean {
		return this.mapByKey.has(key);
	}

	/**
	 * Check if val exists in dict
	 * @param value
	 * @returns true if value exists and false otherwise
	 */
	hasVal(value: T) {
		return this.mapByVal.has(value);
	}

	/**
	 * Delete by key
	 * @param key
	 * @returns true if value is deleted successfully and false otherwise
	 */
	deleteByNum(key: number): boolean {
		const val = this.mapByKey.get(key);
		const result = this.mapByKey.delete(key);

		if (val !== undefined) {
			this.mapByVal.delete(val);
		}

		return result;
	}

	/**
	 * Delete by Value
	 * @param value
	 * @returns true if value is deleted successfully and false otherwise
	 */
	deleteByVal(value: T): boolean {
		const key = this.mapByVal.get(value);
		const result = this.mapByVal.delete(value);

		if (key !== undefined) {
			this.mapByKey.delete(key);
		}

		return result;
	}

	/**
	 * Get iterable key
	 * @returns key iterator
	 */
	keys(): IterableIterator<number> {
		return [...this.mapByKey.entries()].sort((a, b) => a[0] - b[0]).keys();
	}

	/**
	 * Get iterable value
	 * @returns value iterator
	 */
	values(): IterableIterator<[number, T]> {
		return [...this.mapByKey.entries()].sort((a, b) => a[0] - b[0]).values();
	}

	/**
	 * Get iterable key (unordered)
	 * @returns key iterator (unordered)
	 */
	unorderedKeys(): IterableIterator<number> {
		return this.mapByKey.keys();
	}

	/**
	 * Get iterable value (unordered)
	 * @returns value iterator (unordered)
	 */
	unorderedValues(): IterableIterator<T> {
		return this.mapByKey.values();
	}

	/**
	 * Turn map data into a serializable format
	 * @returns map in serializable format
	 */
	toJSON(): { key: number[]; value: T[] } {
		const result: { key: number[]; value: T[] } = {
			key: [],
			value: [],
		};

		for (const [k, v] of this.mapByKey) {
			result.key.push(k);
			result.value.push(v);
		}

		return result;
	}

	/**
	 * Populate map with output of toJSON()
	 * @param json - output of toJSON()
	 */
	fromJSON(json: { key: number[]; value: T[] }) {
		for (let i = 0; i < json.key.length; i++) {
			const key = json.key[i];
			const value = json.value[i];

			this.mapByKey.set(key, value);
			this.mapByVal.set(value, key);
		}
	}

	/**
	 * Generate the next key and set value to dict
	 * @param value - left side value
	 * @returns new key
	 */
	genNextKey(value: T): number {
		const keys = [...this.mapByKey.keys()];
		const newKey = keys.length > 0 ? Math.max(...keys) + 1 : 1;
		this.set(newKey, value);
		return newKey;
	}
}
