import { describe, expect, test, vi } from "vitest";
import {
	authenticate,
	expiryCookieOpts,
	isUserRedirectedFromIssuer,
	onAuthenticationFailed,
	onAuthenticationSuccess,
	validateAccessToken,
} from "./auth";
import { type Configuration, type TokenEndpointResponse, type UserInfoResponse } from "openid-client";
import { type Cookies } from "@sveltejs/kit";

const VALID_ACCESS_TOKEN = "valid-access-token";
const VALID_REFRESH_TOKEN = "valid-refresh-token";
const VALID_CODE_VERIFIER = "valid-code-verifier";
const EXAMPLE_JWT = "valid-subject-id";

describe("Test Helper Functions", () => {
	test("expire cookie options", () => {
		const option = expiryCookieOpts();
		expect(option.maxAge).toBe(0);
	});

	test("is user redirected from issuer", () => {
		const request: Partial<Request> = {
			redirect: "follow",
		};
		const isRedirected: boolean = isUserRedirectedFromIssuer(
			new URL(`https://example.com?session_state=123&code=456`),
			request as Request
		);
		expect(isRedirected).toBe(true);
	});
});

vi.mock("openid-client", () => {
	return {
		fetchUserInfo: vi.fn().mockImplementation((_, token) => {
			if (token === VALID_ACCESS_TOKEN) {
				return Promise.resolve({} as UserInfoResponse);
			} else {
				return Promise.reject(new Error("Invalid access token"));
			}
		}),
		refreshTokenGrant: vi.fn().mockImplementation((_, token) => {
			if (token === VALID_REFRESH_TOKEN) {
				return Promise.resolve({} as TokenEndpointResponse);
			} else {
				return Promise.reject(new Error("Invalid refresh token"));
			}
		}),
		authorizationCodeGrant: vi.fn().mockImplementation((_, _valid_url, code: { pkceCodeVerifier: string }) => {
			if (code.pkceCodeVerifier === VALID_CODE_VERIFIER) {
				return Promise.resolve({} as TokenEndpointResponse);
			} else {
				return Promise.reject(new Error("Invalid code verifier"));
			}
		}),
	};
});

describe("Access Token Validation", () => {
	test("passes with a valid-token", async () => {
		const result = await validateAccessToken({} as Configuration, VALID_ACCESS_TOKEN);

		expect(result).toBe(true);
	});

	test("fails with an invalid access token", async () => {
		const result = await validateAccessToken({} as Configuration, "rubbish");
		expect(result).toBe(false);
	});
});

describe("Authentication Function", () => {
	const cookies = {
		delete: vi.fn(),
		set: vi.fn(),
	};

	test("deletes all cookies when authentication fails", () => {
		const spy = vi.spyOn(cookies, "delete");
		onAuthenticationFailed(cookies as unknown as Cookies);
		expect(spy).toBeCalledTimes(3);
	});

	test("sets token cookies and deletes code_verifier cookie on update", () => {
		const spySet = vi.spyOn(cookies, "set");
		const spyDelete = vi.spyOn(cookies, "delete");
		onAuthenticationSuccess(cookies as unknown as Cookies, "12345", { refresh_token: "12345" });
		expect(spySet).toBeCalledTimes(2);
		expect(spyDelete).toBeCalledTimes(1);
	});

	test("deletes code_verifier on successful authentication without needing to update cookies", () => {
		const spySet = vi.spyOn(cookies, "set");
		const spyDelete = vi.spyOn(cookies, "delete");
		onAuthenticationSuccess(cookies as unknown as Cookies, "12345", { refresh_token: "12345" });
		expect(spySet).toBeCalledTimes(0);
		expect(spyDelete).toBeCalledTimes(1);
	});
});

describe("Authentication Function", () => {
	const cookies = {
		delete: vi.fn(),
		set: vi.fn(),
		get: vi.fn(),
	};

	vi.mock("jwt-decode", () => {
		return {
			jwtDecode: () => {
				return {
					sub: EXAMPLE_JWT,
				};
			},
		};
	});

	const url = new URL("https://example.com");
	const request = {
		redirect: "follow",
	};

	describe("user has access token and refresh token", async () => {
		cookies.get.mockImplementation((key) => {
			if (key === "access_token") return VALID_ACCESS_TOKEN;
			if (key === "refresh_token") return VALID_REFRESH_TOKEN;
			return undefined;
		});

		test("if the access token is valid", async () => {
			const result = await authenticate(
				{} as unknown as Configuration,
				cookies as unknown as Cookies,
				request as Request,
				url
			);

			expect(result.success).toBe(true);
		});

		test("if the access token is invalid, and refreshing fails", async () => {
			cookies.get.mockImplementation((key) => {
				if (key === "access_token") return "rubbish";
				if (key === "refresh_token") return "rubbish";
			});

			const result = await authenticate(
				{} as unknown as Configuration,
				cookies as unknown as Cookies,
				request as Request,
				url
			);

			expect(result.success).toBe(false);
		});

		test("if the access token is invalid, and refreshing succeeds", async () => {
			cookies.get.mockImplementation((key) => {
				if (key === "access_token") return "rubbish";
				if (key === "refresh_token") return VALID_REFRESH_TOKEN;
			});

			const result = await authenticate(
				{} as unknown as Configuration,
				cookies as unknown as Cookies,
				request as Request,
				url
			);

			expect(result.success).toBe(true);
		});
	});

	describe("user has only refresh token and attempts to refresh token", () => {
		test("refreshing succeeded", async () => {
			cookies.get.mockImplementation((key) => {
				if (key === "refresh_token") return VALID_REFRESH_TOKEN;
				return undefined;
			});

			const result = await authenticate(
				{} as Configuration,
				cookies as unknown as Cookies,
				request as Request,
				url
			);

			expect(result.success).toBe(true);
		});

		test("refreshing failed", async () => {
			cookies.get.mockImplementation((key) => {
				if (key === "refresh_token") return "rubbish";
				return undefined;
			});

			const result = await authenticate(
				{} as Configuration,
				cookies as unknown as Cookies,
				request as Request,
				url
			);

			expect(result.success).toBe(false);
		});
	});
});
