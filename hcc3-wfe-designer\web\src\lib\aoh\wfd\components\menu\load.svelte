<script lang="ts">
	import { toast } from "svelte-sonner";
	import { dia } from "rappid/rappid";
	import * as Command from "$lib/aoh/wfd/components/ui/command/index.js";
	import * as Popover from "$lib/aoh/wfd/components/ui/popover/index.js";
	import { workflowData } from "$lib/aoh/wfd/stores/workflowData";
	import { workflowStore, getWorkflowById } from "$lib/aoh/wfd/stores/workflows";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button/index.js";

	let { graph }: { graph: dia.Graph } = $props();
	let open = $state(false);

	const loadWorkflow = (id: string) => {
		const wf = getWorkflowById(id);
		if (wf === undefined) {
			toast.error("Failed to load workflow");
			return;
		}
		graph.fromJSON(wf.designer_json);
		$workflowData = {
			name: wf.name,
			id: wf.id,
			occ_lock: wf.occ_lock,
		};
	};

	let selectedValue = $derived($workflowData.name !== "" ? $workflowData.name : "Load a workflow template...");
</script>

<Popover.Root bind:open>
	<Popover.Trigger
		role="combobox"
		aria-expanded={open}
		class={buttonVariants({ variant: "outline" }) +
			" flex-1 justify-between md:max-w-[200px] md:min-w-[100px] lg:max-w-[300px] lg:min-w-[150px]"}
	>
		{selectedValue}
		<span class="icon-[fa6-solid--caret-down]" aria-hidden="true"></span>
	</Popover.Trigger>
	<Popover.Content class="w-full p-0 md:w-[200px] lg:w-[300px]">
		<Command.Root>
			<Command.Input placeholder="Search workflow templates..." />
			<Command.List>
				<Command.Empty>No workflow templates found.</Command.Empty>
				<Command.Group heading="Workflow Templates">
					{#each $workflowStore as workflow}
						<Command.Item
							value={workflow.name}
							class="aria-selected:bg-primary aria-selected:text-primary-foreground"
							onSelect={() => {
								loadWorkflow(workflow.id);
								open = false;
							}}
						>
							{workflow.name}
						</Command.Item>
					{/each}
				</Command.Group>
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
