<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import Input from "../../components/ui/input/input.svelte";
	import Label from "../../components/ui/label/label.svelte";

	let { activity }: { activity: Activity } = $props();
	let days = $state(activity.getParameter("Days") as number);
	let hours = $state(activity.getParameter("Hours") as number);
	let minutes = $state(activity.getParameter("Minutes") as number);
	let seconds = $state(activity.getParameter("Seconds") as number);

	function preventInvalidChars(e: KeyboardEvent) {
		const invalidKeys = ["e", "E", "+", "-", "."];
		if (invalidKeys.includes(e.key)) {
			e.preventDefault();
		}
	}
	const saveParam = (key: string, value: number) => {
		if (value < 0 || !Number.isInteger(value)) {
			value = Math.max(0, Math.floor(value));
		}
		activity.setParameter(key, value);
	};
</script>

<div class="flex space-x-2">
	<div class="flex-1">
		<Input
			type="number"
			id="days"
			min="0"
			onblur={() => {
				saveParam("Days", days);
			}}
			onkeydown={preventInvalidChars}
			bind:value={days}
		/>
		<Label class="after:!content-[''] !mt-2" for="days">Day(s)</Label>
	</div>

	<div class="flex-1">
		<Input
			type="number"
			id="hours"
			min="0"
			onblur={() => {
				saveParam("Hours", hours);
			}}
			onkeydown={preventInvalidChars}
			bind:value={hours}
		/>
		<Label class="after:!content-[''] !mt-2" for="hours">Hour(s)</Label>
	</div>
	<div class="flex-1">
		<Input
			type="number"
			id="minutes"
			min="0"
			onblur={() => {
				saveParam("Minutes", minutes);
			}}
			onkeydown={preventInvalidChars}
			bind:value={minutes}
		/>
		<Label class="after:!content-[''] !mt-2" for="minutes">Minute(s)</Label>
	</div>
	<div class="flex-1">
		<Input
			type="number"
			id="seconds"
			min="0"
			onblur={() => {
				saveParam("Seconds", seconds);
			}}
			onkeydown={preventInvalidChars}
			bind:value={seconds}
		/>
		<Label class="after:!content-[''] !mt-2" for="seconds">Second(s)</Label>
	</div>
</div>
