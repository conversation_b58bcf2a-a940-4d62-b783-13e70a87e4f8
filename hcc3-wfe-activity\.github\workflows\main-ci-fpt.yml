name: WFE Activity - Main CI FPT

env:
  org_name: hcc3
  app_name: hcc3-wfe-activity-fpt
  mytag: latest

  APP_PORT: 5000
  LOG_LEVEL: debug

  # Temporal
  TEMPORAL_HOST: localhost
  TEMPORAL_PORT: 7233
  TEMPORAL_NAMESPACE: default
  TEMPORAL_TASKQUEUE: taskqueue

  # Hcc3 service dependencies
  DATA_AGG_URL: http://data-agg:5003
  COMMON_URL: http://common:5009
  SOP_URL: http://sop:5005

on:
  workflow_dispatch:

concurrency:
  group: ci-hcc3-wfe-activity-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash

jobs:
  validate-branch:
    runs-on: ubuntu-latest
    outputs:
      run_ci: ${{ steps.check-branch.outputs.run_ci }}
    steps:
      - name: Check if branch name matches sprint pattern
        id: check-branch
        run: |
          echo "GITHUB_REF_NAME=${GITHUB_REF_NAME}"
          if [[ "$GITHUB_REF_NAME" =~ ^release/sprint-[0-9]+-fpt$ ]]; then
            echo "✅ Valid sprint branch"
            echo "run_ci=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Invalid branch. Skipping workflow."
            echo "run_ci=false" >> $GITHUB_OUTPUT
          fi

  build-and-publish-image:
    needs: validate-branch
    if: needs.validate-branch.outputs.run_ci == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          path: ./SRC

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Ensure ECR repository exists
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          aws ecr describe-repositories --repository-names ${{ env.app_name }} || \
          aws ecr create-repository --repository-name ${{ env.app_name }}

      - name: Build and push Docker image (worker)
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ env.mytag }}
        run: |
          cd ./SRC
          docker build . -f docker/worker.Dockerfile -t $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG \
            --build-arg APP_PORT=${{ env.APP_PORT }} \
            --build-arg LOG_LEVEL=${{ env.LOG_LEVEL }} \
            --build-arg TEMPORAL_HOST=${{ env.TEMPORAL_HOST }} \
            --build-arg TEMPORAL_PORT=${{ env.TEMPORAL_PORT }} \
            --build-arg TEMPORAL_NAMESPACE=${{ env.TEMPORAL_NAMESPACE }} \
            --build-arg TEMPORAL_TASKQUEUE=${{ env.TEMPORAL_TASKQUEUE }} \
            --build-arg DATA_AGG_URL=${{ env.DATA_AGG_URL }} \
            --build-arg COMMON_URL=${{ env.COMMON_URL }} \
            --build-arg SOP_URL=${{ env.SOP_URL }}

          docker push $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG

      - name: Post success message to Slack
        if: success()
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ Build succeeded on *${{ github.repository }}* for branch \`${{ github.ref_name }}\`.\
            <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>\"}" \
            ${{ secrets.SLACK_WEBHOOK }}
      - name: Post failure message to Slack
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"❌ Build failed on *${{ github.repository }}* for branch \`${{ github.ref_name }}\`.\
            <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>\"}" \
            ${{ secrets.SLACK_WEBHOOK }}
