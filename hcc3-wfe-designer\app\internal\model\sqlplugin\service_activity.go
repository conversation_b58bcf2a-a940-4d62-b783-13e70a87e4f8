package sqlplugin

import (
	"context"
	"database/sql"
	"encoding/json"

	"github.com/google/uuid"
)

type (
	ServiceActivityRow struct {
		Id              uuid.UUID       `db:"id" json:"id"`
		ServiceName     string          `db:"service_name" json:"service_name"`
		ActivityType    string          `db:"activity_type" json:"activity_type"`
		ActivityIcon    string          `db:"activity_icon" json:"activity_icon"`
		ActivityParam   json.RawMessage `db:"activity_param" json:"activity_param"`
		ActivityResult  json.RawMessage `db:"activity_result" json:"activity_result"`
		TimeoutInSecond float64         `db:"timeout_in_second" json:"timeout_in_second"`
	}

	ServiceActivityFilter struct {
		Id uuid.UUID
	}

	ServiceActivityPaginateFilter struct {
		Limit   int
		Offset  int
		OrderBy string
	}

	ServiceActivity interface {
		InsertIntoServiceActivity(ctx context.Context, rows *ServiceActivityRow) (sql.Result, error)
		UpdateServiceActivity(ctx context.Context, rows *ServiceActivityRow) (sql.Result, error)
		SelectFromServiceActivity(ctx context.Context, filter ServiceActivityFilter) (*ServiceActivityRow, error)
		ListFromServiceActivity(ctx context.Context, filter ServiceActivityPaginateFilter) ([]ServiceActivityRow, error)
		DeleteFromServiceActivity(ctx context.Context, filter ServiceActivityFilter) (sql.Result, error)
		CountFromServiceActivity(ctx context.Context) (int, error)
	}
)
