## HelloWorld World Example

This is a very simple example to get you familiar with the workflow engine. 
Just run the below steps and observe what happens.

### Steps to run this sample:
1) Run a [Temporal service](https://github.com/temporalio/samples-go/tree/main/#how-to-use).
2) Run the following command to start the worker
```
go run helloworld/worker/main.go
```
3) Run the following command to start the example
```
go run helloworld/starter/main.go
```
