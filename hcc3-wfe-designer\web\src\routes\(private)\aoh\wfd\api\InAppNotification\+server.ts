import { json, error } from "@sveltejs/kit";
import type { RequestHandler } from "@sveltejs/kit";
import { StatusCodes } from "http-status-codes";
import { env } from "$env/dynamic/private";

export const GET: RequestHandler = async ({ locals }) => {
	if (!locals.authResult.success) {
		return new Response(
			JSON.stringify({
				message: "Unauthorized",
			}),
			{
				status: StatusCodes.UNAUTHORIZED,
			}
		);
	}

	const bearerAuthorizationString = `Bearer ${locals.authResult.access_token}`;

	const headers = {
		"Content-Type": "application/json",
		Authorization: bearerAuthorizationString,
	};

	const user_promise = await fetch(env.ACTIVITY_AAS_URL + "/admin/users", {
		method: "GET",
		headers,
	});

	const tenant_promise = await fetch(env.ACTIVITY_AAS_URL + "/admin/tenants", {
		method: "GET",
		headers,
	});

	const [user_response, tenant_response] = await Promise.all([user_promise, tenant_promise]);

	if (!user_response.ok || !tenant_response.ok) {
		return error(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to fetch IAMS data");
	}

	const data = {
		users: await user_response.json(),
		tenants: await tenant_response.json(),
	};

	return json(data);
};
