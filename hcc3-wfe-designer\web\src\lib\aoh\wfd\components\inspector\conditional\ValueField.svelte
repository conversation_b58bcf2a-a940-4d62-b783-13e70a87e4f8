<script lang="ts">
	import { onMount } from "svelte";
	import type { ui } from "rappid/rappid";
	import * as Select from "$lib/aoh/wfd/components/ui/select";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { Input } from "$lib/aoh/wfd/components/ui/input";
	import { Switch } from "$lib/aoh/wfd/components/ui/switch";
	import { Flow } from "$lib/aoh/wfd/bpmn/shapes";

	let { inspector, path }: { inspector: ui.Inspector; path: string } = $props();

	const cell = inspector.options.cell as Flow;

	const TypeList = ["Number", "String", "Boolean"];

	let typeValue = $state("");
	let value: string | number | undefined = $state();
	let checked = $state(false);

	onMount(() => {
		typeValue = cell.prop(`${path}Type`) || TypeList[0];
		if (typeValue === "Boolean") {
			checked = cell.prop(path) || false;
		} else {
			value = cell.prop(path);
		}
	});

	const onChangeTypeValue = () => {
		cell.prop(`${path}Type`, typeValue);
		cell.removeProp(path);
	};

	const onChangeValue = () => {
		if (typeValue === "Boolean") {
			cell.prop(path, checked);
		} else {
			cell.prop(path, value);
		}
	};

	const updateCellValue = (fn: () => void) => {
		// shad-cn popover closing animation is slower than jointJS inspector refresh
		// so we need to delay the applyChangeToCell function
		setTimeout(() => {
			fn();
		}, 200);
	};
</script>

<Label for="valueType">Value Type</Label>
<Select.Root
	type="single"
	allowDeselect={false}
	bind:value={typeValue}
	onValueChange={() => updateCellValue(onChangeTypeValue)}
>
	<Select.Trigger class="w-full" id="valueType">
		{typeValue}
	</Select.Trigger>
	<Select.Content>
		<Select.Group>
			{#each TypeList as type}
				<Select.Item value={type} label={type}>{type}</Select.Item>
			{/each}
		</Select.Group>
	</Select.Content>
</Select.Root>
<Label for="value">Value</Label>
{#if typeValue === "Number"}
	<Input type="number" name="value" id="value" bind:value onblur={() => updateCellValue(onChangeValue)} />
{:else if typeValue === "String"}
	<Input type="text" name="value" id="value" bind:value onblur={() => updateCellValue(onChangeValue)} />
{:else if typeValue === "Boolean"}
	<Switch id="value" bind:checked onCheckedChange={() => updateCellValue(onChangeValue)} />
{/if}
