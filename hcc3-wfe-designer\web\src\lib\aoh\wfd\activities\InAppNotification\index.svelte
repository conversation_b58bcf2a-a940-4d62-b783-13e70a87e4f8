<script lang="ts">
	import { onMount } from "svelte";
	import * as Dialog from "$lib/aoh/wfd/components/ui/dialog/index.js";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button";
	import IamsSetting from "$lib/aoh/wfd/activities/InAppNotification/IamsSetting.svelte";
	import { Input } from "$lib/aoh/wfd/components/ui/input";

	let { activity }: { activity: Activity } = $props();
	let title = $state((activity.getParameter("title") as string) || "");
	let body = $state((activity.getParameter("body") as string) || "");

	onMount(async () => {
		if (activity.getUiState("iams_users") == null) {
			const response = await fetch("wfd/api/InAppNotification", {
				method: "GET",
			});
			const data = await response.json();
			activity.setUiState("iams_users", data.users);
			activity.setUiState("iams_tenants", data.tenants);
		}
	});
</script>

<Label for="title">Title</Label>
<Input
	id="title"
	onblur={() => {
		activity.setParameter("title", title);
	}}
	bind:value={title}
/>
<Label for="body">Body</Label>
<Input
	id="body"
	onblur={() => {
		activity.setParameter("body", body);
	}}
	bind:value={body}
/>
<Dialog.Root>
	<Dialog.Trigger class={buttonVariants({ variant: "secondary" }) + " mt-2"}>IAMS Settings</Dialog.Trigger>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>IAMS Settings</Dialog.Title>
		</Dialog.Header>
		<IamsSetting {activity}></IamsSetting>
		<Dialog.Footer>
			<Dialog.Close class={buttonVariants({ variant: "secondary" })}>Close</Dialog.Close>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
