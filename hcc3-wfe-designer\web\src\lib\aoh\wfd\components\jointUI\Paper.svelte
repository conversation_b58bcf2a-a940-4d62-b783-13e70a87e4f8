<script lang="ts">
	import { ActivityBase, Event, Flow, util } from "$lib/aoh/wfd/bpmn/shapes";
	import { dia, ui, shapes } from "rappid/rappid";
	import { onMount } from "svelte";

	interface Props {
		graph: dia.Graph;
		paper?: dia.Paper;
		selection?: ui.Selection;
		snaplines?: ui.Snaplines;
		blankPointerDown?: (event: dia.Event, x: number, y: number) => void;
		blankContextMenu?: (event: dia.Event, x: number, y: number) => void;
		cellPointerDown?: (cellView: dia.CellView, event: dia.Event, x: number, y: number) => void;
		linkMouseEnter?: (linkView: dia.LinkView) => void;
		linkMouseLeave?: (linkView: dia.LinkView) => void;
	}

	let {
		graph,
		paper = $bindable(),
		selection = $bindable(),
		snaplines = $bindable(),
		blankPointerDown,
		blankContextMenu,
		cellPointerDown,
		linkMouseEnter,
		linkMouseLeave,
	}: Props = $props();
	let container: HTMLElement;

	onMount(() => {
		const containerRect = container.getBoundingClientRect();

		paper = new dia.Paper({
			width: containerRect.width + 25,
			height: containerRect.height + 28,
			model: graph,
			gridSize: 10,
			drawGrid: true,
			background: {
				color: "transparent",
			},
			async: true,
			sorting: dia.Paper.sorting.APPROX,
			cellViewNamespace: shapes,
			clickThreshold: 10,
			// Connections
			defaultConnectionPoint: {
				name: "boundary",
				args: { stroke: true },
			},
			defaultLink: new Flow(),
			validateConnection: function (cellViewS, _magnetS, cellViewT, _magnetT, _end) {
				const source = cellViewS?.model;
				const target = cellViewT?.model;

				if (!source || !target) {
					return true;
				}

				if (source.id === target?.id) {
					return false;
				}

				if (source.isLink() || target.isLink()) {
					return false;
				}

				return !(source.get("type") === "wf.Group" || target.get("type") === "wf.Group");
			},
			// Embedding
			embeddingMode: true,
			frontParentOnly: false,
			validateEmbedding: function (childView, parentView) {
				var parentType = parentView.model;
				var childType = childView.model;
				if (parentType instanceof ActivityBase && childType instanceof Event) return true;
				return false;
			},
			// Highlighting
			highlighting: {
				default: {
					name: "mask",
					options: {
						attrs: {
							stroke: "#3498db",
							"stroke-width": 3,
							"stroke-linejoin": "round",
						},
					},
				},
			},
		});

		paper.on({
			"blank:pointerdown": function (event: dia.Event, x: number, y: number) {
				blankPointerDown?.(event, x, y);
			},

			"blank:contextmenu": function (event: dia.Event, x: number, y: number) {
				blankContextMenu?.(event, x, y);
			},

			"cell:pointerdown": function (cellView: dia.CellView, event: dia.Event, x: number, y: number) {
				cellPointerDown?.(cellView, event, x, y);
			},

			"link:mouseenter": function (linkView: dia.LinkView) {
				linkMouseEnter?.(linkView);
			},

			"link:mouseleave": function (linkView: dia.LinkView) {
				linkMouseLeave?.(linkView);
			},

			"link:disconnect": function (
				linkView: dia.LinkView,
				_evt: dia.Event,
				cellView: dia.CellView,
				_: unknown,
				head: dia.LinkEnd
			) {
				// Unsubscribe to Switch order map
				if (head !== "source") {
					return;
				}

				const flow = linkView.model as Flow;
				const source = cellView.model;
				if (util.hasOnDisconnect(source)) {
					source.onDisconnect(flow);
				}
			},
		});

		// Scroller
		const paperScroller = new ui.PaperScroller({
			paper,
			autoResizePaper: true,
			padding: 30,
			scrollWhileDragging: true,
		});
		container.replaceChildren(paperScroller.el);

		// Snaplines
		snaplines = new ui.Snaplines({
			paper,
			usePaperGrid: true,
		});
		snaplines.enable();

		// Selection
		selection = new ui.Selection({
			paper: paper,
			graph: graph,
			useModelGeometry: true,
		});
	});
</script>

<div class="w-full h-full p-2" bind:this={container}></div>
