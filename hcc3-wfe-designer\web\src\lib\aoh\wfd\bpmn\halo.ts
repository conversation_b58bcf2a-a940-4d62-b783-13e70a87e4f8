import { dia, g, ui } from "rappid/rappid";
import {
	Activity,
	CallActivity,
	End,
	Terminate,
	Event,
	Flow,
	Form,
	Parallel,
	Start,
	Switch,
} from "$lib/aoh/wfd/bpmn/shapes";

let halo: ui.Halo;
let paper: dia.Paper;
let graph: dia.Graph;
let cmd: dia.CommandManager;
let openTools: (cellView: dia.CellView, coordinates: g.Point | undefined) => void;

// Center element at cursor
// Function taken from Halo source code of rappid
const centerElementAtCursor = (element: dia.Element, x: number, y: number) => {
	const center = element.getBBox().center();
	const tx = x - center.x;
	const ty = y - center.y;

	element.translate(tx, ty);
};

const dragElement = (evt: dia.Event, x: number, y: number) => {
	const model: dia.Element = halo.eventData(evt)?.model;
	const source: dia.Element = halo.eventData(evt)?.source;
	const view: dia.ElementView = halo.eventData(evt)?.view;
	let viewUnder: dia.ElementView = halo.eventData(evt)?.viewUnder;
	if (!model || !source) {
		return;
	}

	// Reset viewUnder
	if (viewUnder) {
		viewUnder.unhighlight(undefined, { embedding: true });
		halo.eventData(evt, { viewUnder: null });
	}

	centerElementAtCursor(model, x, y);
	view.notify("element:pointermove", evt, x, y);

	const modelUnder = graph.findModelsUnderElement(model).find((el) => el.id !== source.id);
	if (!modelUnder) {
		return;
	}

	// Find view of model under currently dragging element and highlight it
	viewUnder = paper.findViewByModel(modelUnder);
	viewUnder.highlight(undefined, { embedding: true });

	halo.eventData(evt, { viewUnder });
};

const stopDrag = (evt: dia.Event, x: number, y: number) => {
	const viewUnder: dia.ElementView = halo.eventData(evt)?.viewUnder;
	const model: dia.Element = halo.eventData(evt)?.model;
	const view: dia.ElementView = halo.eventData(evt)?.view;
	if (!model || !view) {
		return;
	}
	openTools(view, new g.Point(x, y));

	// Unhighlight and process embed
	if (viewUnder) {
		viewUnder.unhighlight(undefined, { embedding: true });
		viewUnder.model.embed(model);
	}

	// Stop batch
	cmd.storeBatchCommand();
};

const createHaloElementHandle = (name: string, element: dia.Element) => {
	halo.addHandle({
		name,
		position: ui.Halo.HandlePosition.SE,
		icon: `/images/wfd/${name}.png`,
	});
	halo.on(`action:${name}:pointerdown`, (evt: dia.Event, x: number, y: number) => {
		// Start batch
		cmd.initBatchCommand();

		// Position newly created element to the center of cursor
		centerElementAtCursor(element, x, y);

		// Create flow linking source element and new element
		const source = halo.options.cellView.model;
		const flow = new Flow({
			source: {
				id: source.id,
			},
			target: {
				id: element.id,
			},
		});
		graph.addCells([element, flow]);

		const view = paper.findViewByModel(element);
		halo.eventData(evt, {
			model: element,
			view,
			source,
		});
		view.notify("element:pointerdown", evt, x, y);
	});

	halo.on(`action:${name}:pointermove`, dragElement);
	halo.on(`action:${name}:pointerup`, stopDrag);
};

export const initHalo = (
	_halo: ui.Halo,
	_graph: dia.Graph,
	_paper: dia.Paper,
	_cmd: dia.CommandManager,
	_openTools: (cellView: dia.CellView, coordinates: g.Point | undefined) => void
) => {
	halo = _halo;
	paper = _paper;
	graph = _graph;
	cmd = _cmd;
	openTools = _openTools;

	// Initialize halo toolbar
	createHaloElementHandle("switch", new Switch());
	createHaloElementHandle("parallel", new Parallel());
	createHaloElementHandle("activity", new Activity());
	createHaloElementHandle("form", new Form());
	createHaloElementHandle("callActivity", new CallActivity());
	createHaloElementHandle("event", new Event());
	createHaloElementHandle("start", new Start());
	createHaloElementHandle("end", new End());
	createHaloElementHandle("terminate", new Terminate());
};
