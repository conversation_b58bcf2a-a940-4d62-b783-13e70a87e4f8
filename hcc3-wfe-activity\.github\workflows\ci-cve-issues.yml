name: WFE Activity - CI - CVE Issues

env:
    org_name: hcc3
    app_name: hcc3-wfe-activity
    mytag: cveissues

    # application log level and port
    APP_PORT: 5000
    LOG_LEVEL: debug

    # Temporal
    TEMPORAL_HOST: localhost
    TEMPORAL_PORT: 7233
    TEMPORAL_NAMESPACE: default
    TEMPORAL_TASKQUEUE: taskqueue
    # TEMPORAL_WORKFLOW_TYPE: workflow_type
    # TEMPORAL_WORKFLOW_TASKQUEUE: workflow_taskqueue
    # TEMPORAL_ACTIVITY_TASKQUEUE: activity_taskqueue

    # Hcc3
    DATA_AGG_URL: http://data-agg:5003
    COMMON_URL: http://common:5009
    SOP_URL: http://sop:5005


on:
    workflow_dispatch:
      inputs:
        logLevel:
          description: 'Log level'
          required: true
          default: 'warning'
          type: choice
          options:
          - info
          - warning
          - debug
    push:
      branches:
        - 'bugfix/cveissues'
      paths:
        - '**'
        - '!.github/workflows/**'

concurrency:
    group: ci-hcc3-wfe-activity-${{ github.ref }}
    cancel-in-progress: true

defaults:
    run:
        shell: bash

jobs:
    build-and-publish-image:
        name: 'Build & Publish Container Image'
        runs-on: ubuntu-latest

        steps:
        - name: 'Checkout code'
          uses: actions/checkout@v3
          with:
            ref: 'bugfix/cveissues'

        - name: Configure AWS credentials 
          uses: aws-actions/configure-aws-credentials@v3
          with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ap-southeast-1

        - name: Login to Amazon ECR
          id: login-ecr
          uses: aws-actions/amazon-ecr-login@v1

        - name: Get timestamp
          id: get-timestamp
          run: echo "::set-output name=timestamp::$(date +'%s')"
        
        - name: '${{ env.app_name }} - Build and upload container image'
          id: build-image
          env: 
            ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
            # ECR_REPOSITORY: ${{ secrets.REPO_NAME }}
            # IMAGE_TAG: ${{ steps.get-timestamp.outputs.timestamp }}
            IMAGE_TAG: ${{ env.mytag }}
          run: |
              # IMAGE_WITH_TAG="${{ env.image_prefix }}/${{ env.org_name }}/${{ env.app_name }}:$(date +%s)"
              # docker build . --file docker/service.Dockerfile --tag hcc3-wfe-activity:$(date +%s)
              # docker push ${IMAGE_WITH_TAG}
              # docker build . --file docker/worker.Dockerfile --tag $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG \
              # --build-arg KEYCLOAK_AUTH_URL=http://54.169.142.238:8888/realms/AOH/protocol/openid-connect/userinfo
              
              docker build . --file docker/worker.Dockerfile --tag $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG
              docker push $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG